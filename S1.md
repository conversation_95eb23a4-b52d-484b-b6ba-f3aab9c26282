redis如果不存在，应该在查mysql，这里考虑的是redis异常数据丢失，导致的问题。
redis的缓存时间为永久，因为是问题类缓存，答案与问题不会发生变化所以可以永久缓存。
deepseek返回的结果如果要异步同时写入mysql与redis的话，需要保证valve的一致性。我更倾向于先写入mysql在按照回写统一的格式拼装redis的valve。


我期望valve的内容存入为以下格式，且有这些字段。

{
  "question_type": "**",
  "question_text": "**",
  "options_a": "**",
  "options_b": "**",
  "options_c": "**",
  "options_d": "**",
  "options_y": "**",
  "options_n": "**",
  "answer": {
    "*":"*,*,*"
  },
  "analysis": "**", 
  "question_img_raw": "**",
  "question_img": "**",
  "associate": "**"
},

associate的作用是关联键，这个是管理手动添加的值，会填入其他问题的cache_key，用户请求业务时如果命中的问题，关联键存在值，则按照cache_key查询的方式进行redis与mysql的查询。将关联键中的其他问题，一同返回给用户。关联键可能存在多个cache_key，需要一同返回。

associate这个字段需要新建
question_img这个字段是一个图片链接，也是管理员手动添加的值，后期管理员会为不同的问题人工匹配不同的照片。


"answer": {
    "*":"*,*,*"
  },   这里要表达的意思是，如果是多选题，会存在多个答案，需要将多个答案返回json，然后答案之间需要逗号分隔，并不确定具体业务。你可能需要查询deepseek返回的值格式化解析如何执行的，主要查看多选题的答案是如何处理的，因为只有多选题才存在多个答案。