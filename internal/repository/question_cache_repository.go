package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"solve_api/internal/config"
	"solve_api/internal/model"
	"time"

	"github.com/redis/go-redis/v9"
)

type QuestionCacheRepository struct {
	rdb       *redis.Client
	mysqlRepo *QuestionRepository // MySQL作为持久化备份
}

// NewQuestionCacheRepository 创建题目缓存仓库实例
func NewQuestionCacheRepository(rdb *redis.Client, mysqlRepo *QuestionRepository) *QuestionCacheRepository {
	return &QuestionCacheRepository{
		rdb:       rdb,
		mysqlRepo: mysqlRepo,
	}
}

// Set 设置题目到Redis主存储，并异步持久化到MySQL
func (r *QuestionCacheRepository) Set(hash string, question *model.Question) error {
	setStartTime := time.Now()
	fmt.Printf("💾 [Redis性能分析] 开始Set操作 - 缓存键: %s\n", hash)

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)
	fmt.Printf("🔑 [Redis性能分析] 生成Redis键: %s\n", key)

	// 序列化题目数据
	marshalStart := time.Now()
	fmt.Printf("📦 [Redis性能分析] 开始序列化题目数据\n")
	data, err := json.Marshal(question)
	if err != nil {
		return err
	}
	marshalDuration := time.Since(marshalStart)
	fmt.Printf("✅ [Redis性能分析] 数据序列化完成: %v, 大小: %d bytes\n", marshalDuration, len(data))

	// 设置TTL
	ttlStart := time.Now()
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}
	ttlDuration := time.Since(ttlStart)
	fmt.Printf("⏰ [Redis性能分析] TTL设置完成: %v, TTL: %v\n", ttlDuration, ttl)

	// 1. 优先写入Redis主存储
	if r.rdb != nil {
		redisSetStart := time.Now()
		fmt.Printf("📡 [Redis性能分析] 开始写入Redis\n")
		if err := r.rdb.Set(ctx, key, data, ttl).Err(); err != nil {
			redisSetDuration := time.Since(redisSetStart)
			// Redis写入失败，降级到MySQL
			fmt.Printf("⚠️ [Redis性能分析] Redis写入失败，降级到MySQL: %v, 耗时: %v\n", err, redisSetDuration)
			return r.setToMySQLOnly(question)
		}
		redisSetDuration := time.Since(redisSetStart)
		fmt.Printf("✅ [Redis性能分析] Redis写入完成: %v\n", redisSetDuration)

		// 2. 异步持久化到MySQL
		asyncStart := time.Now()
		fmt.Printf("🔄 [Redis性能分析] 启动异步MySQL持久化\n")
		go r.asyncPersistToMySQL(question)
		asyncDuration := time.Since(asyncStart)
		fmt.Printf("✅ [Redis性能分析] 异步任务启动完成: %v\n", asyncDuration)

		totalDuration := time.Since(setStartTime)
		fmt.Printf("🏁 [Redis性能分析] Set操作完成(Redis写入): %v\n", totalDuration)
		return nil
	}

	// Redis不可用，直接写入MySQL
	fmt.Printf("⚠️ [Redis性能分析] Redis不可用，直接写入MySQL\n")
	mysqlStart := time.Now()
	err = r.setToMySQLOnly(question)
	mysqlDuration := time.Since(mysqlStart)
	fmt.Printf("✅ [Redis性能分析] MySQL写入完成: %v\n", mysqlDuration)

	totalDuration := time.Since(setStartTime)
	fmt.Printf("🏁 [Redis性能分析] Set操作完成(MySQL写入): %v\n", totalDuration)

	return err
}

// setToMySQLOnly 仅写入MySQL
func (r *QuestionCacheRepository) setToMySQLOnly(question *model.Question) error {
	if r.mysqlRepo == nil {
		return fmt.Errorf("both Redis and MySQL are unavailable")
	}

	// 检查是否已存在
	existing, err := r.mysqlRepo.GetByCacheKey(question.CacheKey)
	if err != nil {
		return err
	}

	if existing != nil {
		// 已存在，更新
		return r.mysqlRepo.Update(question)
	}

	// 不存在，创建
	return r.mysqlRepo.Create(question)
}

// asyncPersistToMySQL 异步持久化到MySQL（只做MySQL操作，不写Redis）
func (r *QuestionCacheRepository) asyncPersistToMySQL(question *model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	// 检查是否已存在
	existing, err := r.mysqlRepo.GetByCacheKey(question.CacheKey)
	if err != nil {
		fmt.Printf("⚠️ MySQL持久化检查失败: %v\n", err)
		return
	}

	if existing != nil {
		// 已存在，更新
		if err := r.mysqlRepo.Update(question); err != nil {
			fmt.Printf("⚠️ MySQL持久化更新失败: %v\n", err)
		}
	} else {
		// 不存在，创建
		if err := r.mysqlRepo.Create(question); err != nil {
			fmt.Printf("⚠️ MySQL持久化创建失败: %v\n", err)
		}
	}
}

// Get 从Redis主存储获取题目，如果不存在则从MySQL获取并回写Redis
func (r *QuestionCacheRepository) Get(hash string) (*model.Question, error) {
	getStartTime := time.Now()
	fmt.Printf("🔍 [Redis性能分析] 开始Get操作 - 缓存键: %s\n", hash)

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)
	fmt.Printf("🔑 [Redis性能分析] 生成Redis键: %s\n", key)

	// 1. 优先从Redis主存储获取
	if r.rdb != nil {
		redisGetStart := time.Now()
		fmt.Printf("📡 [Redis性能分析] 开始从Redis获取数据\n")
		data, err := r.rdb.Get(ctx, key).Result()
		redisGetDuration := time.Since(redisGetStart)

		if err == nil {
			// Redis命中，反序列化数据
			fmt.Printf("🎯 [Redis性能分析] Redis命中: %v\n", redisGetDuration)

			unmarshalStart := time.Now()
			var question model.Question
			if err := json.Unmarshal([]byte(data), &question); err != nil {
				unmarshalDuration := time.Since(unmarshalStart)
				fmt.Printf("⚠️ Redis数据反序列化失败: %v, 耗时: %v\n", err, unmarshalDuration)
				// 反序列化失败，尝试从MySQL获取
				return r.getFromMySQLAndCache(hash)
			}
			unmarshalDuration := time.Since(unmarshalStart)
			fmt.Printf("✅ [Redis性能分析] 数据反序列化完成: %v\n", unmarshalDuration)

			totalDuration := time.Since(getStartTime)
			fmt.Printf("🏁 [Redis性能分析] Get操作完成(Redis命中): %v\n", totalDuration)
			return &question, nil
		}

		if err != redis.Nil {
			fmt.Printf("⚠️ [Redis性能分析] Redis读取失败: %v, 耗时: %v\n", err, redisGetDuration)
		} else {
			fmt.Printf("❌ [Redis性能分析] Redis未命中: %v\n", redisGetDuration)
		}
		// Redis未命中或读取失败，尝试从MySQL获取
	} else {
		fmt.Printf("⚠️ [Redis性能分析] Redis不可用，直接查询MySQL\n")
	}

	// 2. 从MySQL获取并回写Redis
	fmt.Printf("🗄️ [Redis性能分析] 开始从MySQL获取并回写Redis\n")
	mysqlStart := time.Now()
	result, err := r.getFromMySQLAndCache(hash)
	mysqlDuration := time.Since(mysqlStart)
	fmt.Printf("✅ [Redis性能分析] MySQL查询和回写完成: %v\n", mysqlDuration)

	totalDuration := time.Since(getStartTime)
	fmt.Printf("🏁 [Redis性能分析] Get操作完成(MySQL回源): %v\n", totalDuration)

	return result, err
}

// GetWithAssociates 获取题目及其关联题目（支持关联键功能）
func (r *QuestionCacheRepository) GetWithAssociates(cacheKey string) ([]*model.Question, error) {
	ctx := context.Background()
	key := model.GenerateCacheKey(cacheKey)

	// 1. 优先从Redis获取
	if r.rdb != nil {
		data, err := r.rdb.Get(ctx, key).Result()
		if err == nil {
			// Redis命中，检查是否有关联键
			var question model.Question
			if err := json.Unmarshal([]byte(data), &question); err == nil {
				// 直接返回单个题目（移除关联功能）
				return []*model.Question{&question}, nil
			}
		}
	}

	// 2. Redis未命中，从MySQL获取
	questions, err := r.mysqlRepo.GetWithAssociates(cacheKey)
	if err != nil || len(questions) == 0 {
		return nil, err
	}

	// 3. 将主题目回写到Redis（只回写一次，避免重复）
	if len(questions) > 0 {
		r.cacheQuestionToRedisOnce(questions[0])
	}

	return questions, nil
}

// getFromMySQLAndCache 从MySQL获取数据并回写到Redis
func (r *QuestionCacheRepository) getFromMySQLAndCache(hash string) (*model.Question, error) {
	if r.mysqlRepo == nil {
		return nil, nil // MySQL不可用，返回未找到
	}

	// 从MySQL获取
	question, err := r.mysqlRepo.GetByCacheKey(hash)
	if err != nil {
		return nil, err
	}

	if question == nil {
		return nil, nil // MySQL中也不存在
	}

	// 异步回写到Redis（避免重复写入）
	go r.asyncCacheToRedisOnce(hash, question)

	return question, nil
}

// getAssociatesFromMySQL 从MySQL获取关联题目
func (r *QuestionCacheRepository) getAssociatesFromMySQL(cacheKey string, mainQuestion *model.Question) ([]*model.Question, error) {
	// 从MySQL获取完整的关联题目
	questions, err := r.mysqlRepo.GetWithAssociates(cacheKey)
	if err != nil {
		// 如果MySQL查询失败，至少返回Redis中的主题目
		return []*model.Question{mainQuestion}, nil
	}

	// 将关联题目信息合并到Redis缓存中
	if len(questions) > 1 {
		// 有关联题目，更新Redis缓存包含关联信息
		r.cacheAssociatedQuestions(questions)
	}

	return questions, nil
}

// cacheQuestionToRedis 将题目缓存到Redis
func (r *QuestionCacheRepository) cacheQuestionToRedis(question *model.Question) {
	if r.rdb == nil {
		return
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(question.CacheKey)

	data, err := json.Marshal(question)
	if err != nil {
		fmt.Printf("⚠️ 序列化题目数据失败: %v\n", err)
		return
	}

	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	if err := r.rdb.Set(ctx, key, data, ttl).Err(); err != nil {
		fmt.Printf("⚠️ Redis缓存失败: %v\n", err)
	}
}

// cacheQuestionToRedisOnce 将题目缓存到Redis（避免重复写入）
func (r *QuestionCacheRepository) cacheQuestionToRedisOnce(question *model.Question) {
	if r.rdb == nil {
		return
	}

	// 先检查是否已存在，避免重复写入
	if exists, _ := r.Exists(question.CacheKey); exists {
		return // 已存在，不重复写入
	}

	// 不存在才写入
	r.cacheQuestionToRedis(question)
}

// cacheAssociatedQuestions 缓存关联题目信息
func (r *QuestionCacheRepository) cacheAssociatedQuestions(questions []*model.Question) {
	// 为每个题目单独缓存，但包含关联信息标识
	for _, question := range questions {
		r.cacheQuestionToRedis(question)
	}
}

// asyncCacheToRedis 异步回写到Redis
func (r *QuestionCacheRepository) asyncCacheToRedis(hash string, question *model.Question) {
	if r.rdb == nil {
		return
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 序列化数据
	data, err := json.Marshal(question)
	if err != nil {
		fmt.Printf("⚠️ 回写Redis序列化失败: %v\n", err)
		return
	}

	// 设置TTL
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	// 回写到Redis
	if err := r.rdb.Set(ctx, key, data, ttl).Err(); err != nil {
		fmt.Printf("⚠️ 回写Redis失败: %v\n", err)
	}
}

// asyncCacheToRedisOnce 异步回写到Redis（避免重复写入）
func (r *QuestionCacheRepository) asyncCacheToRedisOnce(hash string, question *model.Question) {
	if r.rdb == nil {
		return
	}

	// 先检查是否已存在，避免重复写入
	if exists, _ := r.Exists(hash); exists {
		return // 已存在，不重复写入
	}

	// 不存在才回写
	r.asyncCacheToRedis(hash, question)
}

// Delete 删除题目（同时删除Redis和MySQL）
func (r *QuestionCacheRepository) Delete(hash string) error {
	var redisErr, mysqlErr error

	// 1. 删除Redis中的数据
	if r.rdb != nil {
		ctx := context.Background()
		key := model.GenerateCacheKey(hash)
		redisErr = r.rdb.Del(ctx, key).Err()
		if redisErr != nil {
			fmt.Printf("⚠️ Redis删除失败: %v\n", redisErr)
		}
	}

	// 2. 删除MySQL中的数据
	if r.mysqlRepo != nil {
		question, err := r.mysqlRepo.GetByCacheKey(hash)
		if err == nil && question != nil {
			mysqlErr = r.mysqlRepo.Delete(question.ID)
			if mysqlErr != nil {
				fmt.Printf("⚠️ MySQL删除失败: %v\n", mysqlErr)
			}
		}
	}

	// 如果两者都失败，返回错误
	if redisErr != nil && mysqlErr != nil {
		return fmt.Errorf("both Redis and MySQL delete failed: redis=%v, mysql=%v", redisErr, mysqlErr)
	}

	return nil
}

// Exists 检查缓存是否存在
func (r *QuestionCacheRepository) Exists(hash string) (bool, error) {
	if r.rdb == nil {
		return false, nil // Redis不可用时返回false
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	count, err := r.rdb.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// SetTTL 设置缓存过期时间
func (r *QuestionCacheRepository) SetTTL(hash string, ttl time.Duration) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.Expire(ctx, key, ttl).Err()
}

// GetTTL 获取缓存剩余时间
func (r *QuestionCacheRepository) GetTTL(hash string) (time.Duration, error) {
	if r.rdb == nil {
		return 0, nil // Redis不可用时返回0
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.TTL(ctx, key).Result()
}

// GetKeys 获取所有题目缓存键
func (r *QuestionCacheRepository) GetKeys(pattern string) ([]string, error) {
	if r.rdb == nil {
		return nil, nil // Redis不可用时返回空
	}

	ctx := context.Background()
	if pattern == "" {
		pattern = "question:*"
	}

	return r.rdb.Keys(ctx, pattern).Result()
}

// Clear 清空所有题目缓存
func (r *QuestionCacheRepository) Clear() error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return r.rdb.Del(ctx, keys...).Err()
}

// GetCacheStats 获取缓存统计信息
func (r *QuestionCacheRepository) GetCacheStats() (map[string]interface{}, error) {
	if r.rdb == nil {
		return map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}, nil
	}

	ctx := context.Background()

	// 获取所有题目缓存键
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"redis_available": true,
		"total_keys":      len(keys),
	}

	// 获取Redis内存使用情况
	info, err := r.rdb.Info(ctx, "memory").Result()
	if err == nil {
		stats["memory_info"] = info
	}

	return stats, nil
}

// BatchSet 批量设置到Redis主存储，并异步持久化到MySQL
func (r *QuestionCacheRepository) BatchSet(questions map[string]*model.Question) error {
	// 1. 批量写入Redis
	var redisErr error
	if r.rdb != nil {
		ctx := context.Background()
		pipe := r.rdb.Pipeline()

		ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
		if ttl <= 0 {
			ttl = 7 * 24 * time.Hour // 默认7天
		}

		for hash, question := range questions {
			key := model.GenerateCacheKey(hash)
			data, err := json.Marshal(question)
			if err != nil {
				continue // 跳过序列化失败的数据
			}
			pipe.Set(ctx, key, data, ttl)
		}

		_, redisErr = pipe.Exec(ctx)
		if redisErr != nil {
			fmt.Printf("⚠️ Redis批量写入失败: %v\n", redisErr)
		}
	}

	// 2. 异步批量持久化到MySQL
	go r.asyncBatchPersistToMySQL(questions)

	// 如果Redis写入失败且MySQL不可用，返回错误
	if redisErr != nil && r.mysqlRepo == nil {
		return redisErr
	}

	return nil
}

// asyncBatchPersistToMySQL 异步批量持久化到MySQL
func (r *QuestionCacheRepository) asyncBatchPersistToMySQL(questions map[string]*model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	for _, question := range questions {
		// 检查是否已存在
		existing, err := r.mysqlRepo.GetByCacheKey(question.CacheKey)
		if err != nil {
			fmt.Printf("⚠️ MySQL批量持久化检查失败: %v\n", err)
			continue
		}

		if existing != nil {
			// 已存在，更新
			if err := r.mysqlRepo.Update(question); err != nil {
				fmt.Printf("⚠️ MySQL批量持久化更新失败: %v\n", err)
			}
		} else {
			// 不存在，创建
			if err := r.mysqlRepo.Create(question); err != nil {
				fmt.Printf("⚠️ MySQL批量持久化创建失败: %v\n", err)
			}
		}
	}
}

// BatchGet 批量获取，优先从Redis，缺失的从MySQL补充
func (r *QuestionCacheRepository) BatchGet(hashes []string) (map[string]*model.Question, error) {
	questions := make(map[string]*model.Question)
	var missedHashes []string

	// 1. 先从Redis批量获取
	if r.rdb != nil {
		ctx := context.Background()
		pipe := r.rdb.Pipeline()

		// 构建键列表
		keys := make([]string, len(hashes))
		for i, hash := range hashes {
			keys[i] = model.GenerateCacheKey(hash)
		}

		// 批量获取
		for _, key := range keys {
			pipe.Get(ctx, key)
		}

		results, err := pipe.Exec(ctx)
		if err != nil && err != redis.Nil {
			fmt.Printf("⚠️ Redis批量读取失败: %v\n", err)
		} else {
			// 解析Redis结果
			for i, result := range results {
				if cmd, ok := result.(*redis.StringCmd); ok {
					data, err := cmd.Result()
					if err == nil {
						var question model.Question
						if json.Unmarshal([]byte(data), &question) == nil {
							questions[hashes[i]] = &question
						} else {
							missedHashes = append(missedHashes, hashes[i])
						}
					} else {
						missedHashes = append(missedHashes, hashes[i])
					}
				} else {
					missedHashes = append(missedHashes, hashes[i])
				}
			}
		}
	} else {
		// Redis不可用，所有hash都需要从MySQL获取
		missedHashes = hashes
	}

	// 2. 从MySQL获取缺失的数据
	if len(missedHashes) > 0 && r.mysqlRepo != nil {
		mysqlQuestions := r.batchGetFromMySQL(missedHashes)

		// 合并结果并异步回写Redis
		for hash, question := range mysqlQuestions {
			questions[hash] = question
			go r.asyncCacheToRedis(hash, question)
		}
	}

	return questions, nil
}

// batchGetFromMySQL 从MySQL批量获取数据
func (r *QuestionCacheRepository) batchGetFromMySQL(hashes []string) map[string]*model.Question {
	questions := make(map[string]*model.Question)

	for _, hash := range hashes {
		question, err := r.mysqlRepo.GetByHash(hash)
		if err == nil && question != nil {
			questions[hash] = question
		}
	}

	return questions
}
