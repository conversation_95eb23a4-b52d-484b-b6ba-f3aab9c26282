package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"solve_api/internal/config"
	"solve_api/internal/model"
)

// QuestionCacheRepositoryRefactored 重构后的题目缓存仓库
// 架构原则：
// 1. Redis为主存储，所有读写操作优先Redis
// 2. MySQL为持久化层，仅用于Redis的数据持久化
// 3. 单一数据流向：Redis → MySQL（异步持久化）
type QuestionCacheRepositoryRefactored struct {
	rdb       *redis.Client
	mysqlRepo *QuestionRepository // 仅用于持久化
}

// NewQuestionCacheRepositoryRefactored 创建重构后的题目缓存仓库
func NewQuestionCacheRepositoryRefactored(rdb *redis.Client, mysqlRepo *QuestionRepository) *QuestionCacheRepositoryRefactored {
	return &QuestionCacheRepositoryRefactored{
		rdb:       rdb,
		mysqlRepo: mysqlRepo,
	}
}

// Set 设置题目到Redis主存储，并异步持久化到MySQL
func (r *QuestionCacheRepositoryRefactored) Set(hash string, question *model.Question) error {
	fmt.Printf("💾 [Redis主存储] 开始Set操作 - 缓存键: %s\n", hash)

	// 1. 写入Redis主存储
	if err := r.setToRedis(hash, question); err != nil {
		return fmt.Errorf("Redis主存储写入失败: %w", err)
	}
	fmt.Printf("✅ [Redis主存储] 写入成功\n")

	// 2. 异步持久化到MySQL
	go r.asyncPersistToMySQL(question)

	return nil
}

// Get 从Redis主存储获取题目
func (r *QuestionCacheRepositoryRefactored) Get(hash string) (*model.Question, error) {
	if r.rdb == nil {
		return nil, fmt.Errorf("Redis不可用")
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	data, err := r.rdb.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 未找到
		}
		return nil, err
	}

	var question model.Question
	if err := json.Unmarshal([]byte(data), &question); err != nil {
		return nil, err
	}

	return &question, nil
}

// GetByID 根据ID从Redis获取题目（需要先查MySQL获取CacheKey）
func (r *QuestionCacheRepositoryRefactored) GetByID(id uint) (*model.Question, error) {
	// 从MySQL获取CacheKey
	mysqlQuestion, err := r.mysqlRepo.GetByID(id)
	if err != nil || mysqlQuestion == nil {
		return nil, err
	}

	// 从Redis获取完整数据
	return r.Get(mysqlQuestion.CacheKey)
}

// Delete 从Redis删除题目
func (r *QuestionCacheRepositoryRefactored) Delete(hash string) error {
	if r.rdb == nil {
		return nil
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.Del(ctx, key).Err()
}

// Exists 检查Redis中是否存在指定键
func (r *QuestionCacheRepositoryRefactored) Exists(hash string) (bool, error) {
	if r.rdb == nil {
		return false, nil
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	count, err := r.rdb.Exists(ctx, key).Result()
	return count > 0, err
}

// BatchSet 批量设置到Redis主存储
func (r *QuestionCacheRepositoryRefactored) BatchSet(questions map[string]*model.Question) error {
	if r.rdb == nil {
		return fmt.Errorf("Redis不可用")
	}

	ctx := context.Background()
	pipe := r.rdb.Pipeline()

	ttl := r.getTTL()
	for hash, question := range questions {
		key := model.GenerateCacheKey(hash)
		data, err := json.Marshal(question)
		if err != nil {
			continue // 跳过序列化失败的数据
		}
		pipe.Set(ctx, key, data, ttl)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("Redis批量写入失败: %w", err)
	}

	// 异步批量持久化到MySQL
	go r.asyncBatchPersistToMySQL(questions)

	return nil
}

// GetCacheStats 获取缓存统计信息
func (r *QuestionCacheRepositoryRefactored) GetCacheStats() (map[string]interface{}, error) {
	if r.rdb == nil {
		return map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}, nil
	}

	ctx := context.Background()

	// 统计question:*键的数量
	keys, err := r.rdb.Keys(ctx, "question:*").Result()
	if err != nil {
		return nil, err
	}

	// 获取Redis内存使用情况
	info, err := r.rdb.Info(ctx, "memory").Result()
	if err != nil {
		info = "无法获取内存信息"
	}

	return map[string]interface{}{
		"redis_available": true,
		"total_keys":      len(keys),
		"memory_info":     info,
	}, nil
}

// Clear 清空所有题目缓存
func (r *QuestionCacheRepositoryRefactored) Clear() error {
	if r.rdb == nil {
		return nil
	}

	ctx := context.Background()

	// 获取所有question:*键
	keys, err := r.rdb.Keys(ctx, "question:*").Result()
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	// 批量删除
	return r.rdb.Del(ctx, keys...).Err()
}

// setToRedis 写入Redis
func (r *QuestionCacheRepositoryRefactored) setToRedis(hash string, question *model.Question) error {
	if r.rdb == nil {
		return fmt.Errorf("Redis不可用")
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 序列化题目数据
	data, err := json.Marshal(question)
	if err != nil {
		return err
	}

	// 设置TTL
	ttl := r.getTTL()

	// 写入Redis
	return r.rdb.Set(ctx, key, data, ttl).Err()
}

// asyncPersistToMySQL 异步持久化到MySQL（只做MySQL操作）
func (r *QuestionCacheRepositoryRefactored) asyncPersistToMySQL(question *model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	fmt.Printf("🗄️ [MySQL持久化] 开始异步持久化 - 题目ID: %d\n", question.ID)

	// 检查MySQL中是否已存在
	existing, err := r.mysqlRepo.GetByCacheKey(question.CacheKey)
	if err != nil {
		fmt.Printf("⚠️ [MySQL持久化] 检查失败: %v\n", err)
		return
	}

	if existing != nil {
		// 已存在，更新
		if err := r.mysqlRepo.Update(question); err != nil {
			fmt.Printf("⚠️ [MySQL持久化] 更新失败: %v\n", err)
		} else {
			fmt.Printf("✅ [MySQL持久化] 更新成功 - 题目ID: %d\n", question.ID)
		}
	} else {
		// 不存在，创建
		if err := r.mysqlRepo.Create(question); err != nil {
			fmt.Printf("⚠️ [MySQL持久化] 创建失败: %v\n", err)
		} else {
			fmt.Printf("✅ [MySQL持久化] 创建成功 - 题目ID: %d\n", question.ID)
		}
	}
}

// asyncBatchPersistToMySQL 异步批量持久化到MySQL
func (r *QuestionCacheRepositoryRefactored) asyncBatchPersistToMySQL(questions map[string]*model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	fmt.Printf("🗄️ [MySQL持久化] 开始批量异步持久化 - 数量: %d\n", len(questions))

	for _, question := range questions {
		r.asyncPersistToMySQL(question)
	}

	fmt.Printf("✅ [MySQL持久化] 批量异步持久化完成\n")
}

// getTTL 获取TTL配置
func (r *QuestionCacheRepositoryRefactored) getTTL() time.Duration {
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}
	return ttl
}

// LoadFromMySQL 从MySQL加载数据到Redis（用于系统初始化或缓存预热）
func (r *QuestionCacheRepositoryRefactored) LoadFromMySQL(limit int) error {
	if r.mysqlRepo == nil {
		return fmt.Errorf("MySQL仓库不可用")
	}

	fmt.Printf("🔄 [缓存预热] 开始从MySQL加载数据到Redis - 限制: %d\n", limit)

	// 从MySQL获取最近的题目
	questions, _, err := r.mysqlRepo.List(0, limit)
	if err != nil {
		return fmt.Errorf("从MySQL获取数据失败: %w", err)
	}

	if len(questions) == 0 {
		fmt.Printf("✅ [缓存预热] 无数据需要加载\n")
		return nil
	}

	// 批量写入Redis
	questionMap := make(map[string]*model.Question)
	for _, question := range questions {
		questionMap[question.CacheKey] = question
	}

	if err := r.BatchSet(questionMap); err != nil {
		return fmt.Errorf("批量写入Redis失败: %w", err)
	}

	fmt.Printf("✅ [缓存预热] 完成 - 加载了 %d 个题目\n", len(questions))
	return nil
}

// SyncToMySQL 手动同步Redis数据到MySQL（用于数据修复）
func (r *QuestionCacheRepositoryRefactored) SyncToMySQL() error {
	if r.rdb == nil || r.mysqlRepo == nil {
		return fmt.Errorf("Redis或MySQL不可用")
	}

	ctx := context.Background()

	// 获取所有Redis键
	keys, err := r.rdb.Keys(ctx, "question:*").Result()
	if err != nil {
		return err
	}

	fmt.Printf("🔄 [数据同步] 开始同步Redis到MySQL - 键数量: %d\n", len(keys))

	for _, key := range keys {
		// 从Redis获取数据
		data, err := r.rdb.Get(ctx, key).Result()
		if err != nil {
			continue
		}

		var question model.Question
		if err := json.Unmarshal([]byte(data), &question); err != nil {
			continue
		}

		// 异步持久化到MySQL
		go r.asyncPersistToMySQL(&question)
	}

	fmt.Printf("✅ [数据同步] 同步任务已启动\n")
	return nil
}

// GetWithAssociates 兼容旧接口（从Redis获取）
func (r *QuestionCacheRepositoryRefactored) GetWithAssociates(cacheKey string) ([]*model.Question, error) {
	question, err := r.Get(cacheKey)
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, nil
	}
	return []*model.Question{question}, nil
}
