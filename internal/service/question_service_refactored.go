package service

import (
	"fmt"
	"time"

	"solve_api/internal/model"
	"solve_api/internal/repository"
)

// QuestionServiceRefactored 重构后的题目服务
// 架构原则：
// 1. Redis为主存储，所有读写操作优先Redis
// 2. MySQL为持久化层，仅用于Redis的数据持久化和题库管理
// 3. 单一数据流向：Redis → MySQL（异步持久化）
type QuestionServiceRefactored struct {
	questionCacheRepo *repository.QuestionCacheRepository
	questionRepo      *repository.QuestionRepository
	userRepo          *repository.UserRepository
	balanceLogRepo    *repository.BalanceLogRepository
	aiService         *AIService
}

// NewQuestionServiceRefactored 创建重构后的题目服务
func NewQuestionServiceRefactored(
	questionCacheRepo *repository.QuestionCacheRepository,
	questionRepo *repository.QuestionRepository,
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
	aiService *AIService,
) *QuestionServiceRefactored {
	return &QuestionServiceRefactored{
		questionCacheRepo: questionCacheRepo,
		questionRepo:      questionRepo,
		userRepo:          userRepo,
		balanceLogRepo:    balanceLogRepo,
		aiService:         aiService,
	}
}

// Search 拍照搜题主要业务逻辑 - 重构版本：Redis主存储，MySQL持久化
func (s *QuestionServiceRefactored) Search(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()
	fmt.Printf("🚀 [搜题流程] 开始 - 用户ID: %d, 图片URL: %s\n", userID, req.ImageURL)

	// 1. 验证图片URL
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}

	// 2. 调用Qwen-VL获取题目结构
	fmt.Printf("🎯 [搜题流程] 调用Qwen-VL进行图像识别\n")
	qwenResult, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}

	// 3. 预处理题目数据并生成缓存键
	fmt.Printf("🔧 [搜题流程] 预处理题目数据\n")
	preprocessed, err := model.PreprocessQwenResult(qwenResult.Structure)
	if err != nil || !preprocessed.IsValid {
		return nil, fmt.Errorf(preprocessed.ErrorMessage)
	}
	cacheKey := model.GenerateCacheKeyFromPreprocessed(preprocessed)
	fmt.Printf("🔑 [搜题流程] 缓存键: %s\n", cacheKey)

	// 4. 查找Redis缓存（主存储）
	fmt.Printf("🔍 [搜题流程] 查找Redis主存储\n")
	question, err := s.questionCacheRepo.Get(cacheKey)
	if err == nil && question != nil {
		// Redis命中，直接返回
		fmt.Printf("✅ [搜题流程] Redis命中，直接返回\n")
		go s.questionRepo.IncrementResponse(question.ID) // 异步更新响应次数
		processTime := time.Since(startTime).Milliseconds()
		return question.ToSearchResponse(true, processTime), nil
	}
	fmt.Printf("❌ [搜题流程] Redis未命中，需要创建新题目\n")

	// 5. Redis未命中，调用DeepSeek解析新题目
	fmt.Printf("🧠 [搜题流程] 调用DeepSeek解析题目\n")
	deepseekResult, err := s.aiService.CallDeepseekWithLogID(qwenResult, qwenResult.LogID)
	if err != nil {
		return nil, fmt.Errorf("题目解析失败: %w", err)
	}

	// 6. 创建新题目并保存到Redis（主存储）
	fmt.Printf("📝 [搜题流程] 创建新题目\n")
	newQuestion := &model.Question{
		SourceModel: "qwen-vl-plus,deepseek-chat",
	}
	newQuestion.FromPreprocessedWithDeepSeek(preprocessed, req.ImageURL, qwenResult.RawContent, deepseekResult)

	// 保存到Redis主存储（会自动异步持久化到MySQL）
	fmt.Printf("💾 [搜题流程] 保存到Redis主存储\n")
	if err := s.questionCacheRepo.Set(newQuestion.CacheKey, newQuestion); err != nil {
		return nil, fmt.Errorf("保存题目失败: %w", err)
	}

	// 7. 扣费处理
	if err := s.processPayment(userID, model.ApplicationTypePhotoSearch); err != nil {
		fmt.Printf("⚠️ 扣费失败: %v\n", err)
	}

	// 8. 构建响应
	processTime := time.Since(startTime).Milliseconds()
	response := newQuestion.ToSearchResponse(false, processTime)
	response.QwenRawContent = qwenResult.RawContent
	response.DeepseekRawContent = deepseekResult.RawContent

	fmt.Printf("🏁 [搜题流程] 完成，总耗时: %v\n", time.Since(startTime))
	return response, nil
}

// processPayment 处理扣费
func (s *QuestionServiceRefactored) processPayment(userID uint, serviceType int) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 获取服务价格（这里简化处理，使用固定价格）
	price := 0.01 // 默认0.01元/次

	// 检查余额
	if user.Balance < price {
		return fmt.Errorf("余额不足")
	}

	// 扣费
	user.Balance -= price
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("扣费失败: %w", err)
	}

	// 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -price,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: "拍照搜题服务费用",
		RelatedID:   0, // 可以关联题目ID
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 日志记录失败不影响主流程
		return nil
	}

	return nil
}

// GetByID 根据ID获取题目
func (s *QuestionServiceRefactored) GetByID(id uint) (*model.QuestionResponse, error) {
	// 优先从Redis获取
	question, err := s.questionCacheRepo.GetByID(id)
	if err == nil && question != nil {
		return question.ToResponse(), nil
	}

	// Redis未命中，从MySQL获取
	question, err = s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %w", err)
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	// 异步回写到Redis
	go s.questionCacheRepo.Set(question.CacheKey, question)

	return question.ToResponse(), nil
}

// GetList 获取题目列表（从MySQL获取，用于管理）
func (s *QuestionServiceRefactored) GetList(page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// SearchQuestions 搜索题目（从MySQL搜索，用于管理）
func (s *QuestionServiceRefactored) SearchQuestions(keyword string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.Search(keyword, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索题目失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// CreateManagementQuestion 创建题库管理题目（直接写MySQL，然后同步到Redis）
func (s *QuestionServiceRefactored) CreateManagementQuestion(req *model.QuestionManagementRequest) (*model.Question, error) {
	// 创建到MySQL
	question, err := s.questionRepo.CreateManagementQuestion(req)
	if err != nil {
		return nil, err
	}

	// 同步到Redis主存储
	go s.questionCacheRepo.Set(question.CacheKey, question)

	return question, nil
}

// UpdateManagementQuestion 更新题库管理题目（更新MySQL，然后同步到Redis）
func (s *QuestionServiceRefactored) UpdateManagementQuestion(id uint, req *model.QuestionUpdateRequest) (*model.Question, error) {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.QuestionType != nil {
		updates["question_type"] = *req.QuestionType
	}
	if req.QuestionText != nil {
		updates["question_text"] = *req.QuestionText
	}
	if req.QuestionImgRaw != nil {
		updates["question_img_raw"] = *req.QuestionImgRaw
	}
	if req.OptionsA != nil {
		updates["options_a"] = *req.OptionsA
	}
	if req.OptionsB != nil {
		updates["options_b"] = *req.OptionsB
	}
	if req.OptionsC != nil {
		updates["options_c"] = *req.OptionsC
	}
	if req.OptionsD != nil {
		updates["options_d"] = *req.OptionsD
	}
	if req.OptionsY != nil {
		updates["options_y"] = *req.OptionsY
	}
	if req.OptionsN != nil {
		updates["options_n"] = *req.OptionsN
	}
	if req.Answer != nil {
		updates["answer"] = *req.Answer
	}
	if req.Analysis != nil {
		updates["analysis"] = *req.Analysis
	}

	// 执行更新MySQL
	if err := s.questionRepo.UpdateQuestion(id, updates); err != nil {
		return nil, fmt.Errorf("更新题目失败: %w", err)
	}

	// 获取更新后的题目
	updatedQuestion, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 同步到Redis主存储
	go s.questionCacheRepo.Set(updatedQuestion.CacheKey, updatedQuestion)

	return updatedQuestion, nil
}

// DeleteQuestion 删除题目（从MySQL删除，然后从Redis删除）
func (s *QuestionServiceRefactored) DeleteQuestion(id uint) error {
	// 获取题目信息
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return err
	}
	if question == nil {
		return fmt.Errorf("题目不存在")
	}

	// 从MySQL删除（软删除）
	if err := s.questionRepo.Delete(id); err != nil {
		return err
	}

	// 从Redis删除
	go s.questionCacheRepo.Delete(question.CacheKey)

	return nil
}

// GetStats 获取题目统计信息
func (s *QuestionServiceRefactored) GetStats() (map[string]interface{}, error) {
	// 获取MySQL统计
	totalCount, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %w", err)
	}

	subjectCount, err := s.questionRepo.GetCountBySubject()
	if err != nil {
		return nil, fmt.Errorf("获取学科统计失败: %w", err)
	}

	gradeCount, err := s.questionRepo.GetCountByGrade()
	if err != nil {
		return nil, fmt.Errorf("获取年级统计失败: %w", err)
	}

	difficultyCount, err := s.questionRepo.GetCountByDifficulty()
	if err != nil {
		return nil, fmt.Errorf("获取难度统计失败: %w", err)
	}

	// 获取Redis缓存统计
	cacheStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		cacheStats = map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}
	}

	return map[string]interface{}{
		"total_questions":  totalCount,
		"subject_count":    subjectCount,
		"grade_count":      gradeCount,
		"difficulty_count": difficultyCount,
		"cache_stats":      cacheStats,
	}, nil
}

// ClearCache 清空Redis缓存
func (s *QuestionServiceRefactored) ClearCache() error {
	return s.questionCacheRepo.Clear()
}
