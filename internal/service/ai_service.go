package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"
)

type AIService struct {
	modelConfigRepo *repository.ModelConfigRepository
	aiLogService    *AILogService
}

// NewAIService 创建AI服务实例
func NewAIService(modelConfigRepo *repository.ModelConfigRepository, aiLogService *AILogService) *AIService {
	return &AIService{
		modelConfigRepo: modelConfigRepo,
		aiLogService:    aiLogService,
	}
}

// QwenVLResult Qwen-VL调用结果
type QwenVLResult struct {
	Structure  *model.PreprocessedQuestion
	RawContent string // 原始content字符串
	LogID      string // 日志ID
}

// DeepseekResult Deepseek调用结果（支持S1需求）
type DeepseekResult struct {
	Analysis     string            // 题目解析
	Answer       string            // 正确答案（支持多选题格式：A,B,C）
	Options      map[string]string // 选项信息（支持A/B/C/D/Y/N）
	QuestionType string            // 题目类型
	RawContent   string            // 原始content字符串
}

// DeepSeek请求结构体定义（使用标准OpenAI格式）
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponseFormat struct {
	Type string `json:"type"`
}

type DeepSeekRequest struct {
	Model            string                  `json:"model"`
	Messages         []DeepSeekMessage       `json:"messages"`
	Temperature      *float64                `json:"temperature,omitempty"`
	TopP             *float64                `json:"top_p,omitempty"`
	TopK             *int                    `json:"top_k,omitempty"`
	MaxTokens        *int                    `json:"max_tokens,omitempty"`
	DoSample         *bool                   `json:"do_sample,omitempty"`
	ResponseFormat   *DeepSeekResponseFormat `json:"response_format,omitempty"`
	FrequencyPenalty *float64                `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64                `json:"presence_penalty,omitempty"`
	Stream           *bool                   `json:"stream,omitempty"`
	Stop             interface{}             `json:"stop,omitempty"`
}

// CallQwenVL 调用Qwen-VL模型进行图像识别
func (s *AIService) CallQwenVL(imageURL string) (*QwenVLResult, error) {
	totalStartTime := time.Now()
	fmt.Printf("🚀 [Qwen性能分析] 开始CallQwenVL - 图片URL: %s\n", imageURL)

	// 创建日志条目
	logCreateStart := time.Now()
	var logID string
	if s.aiLogService != nil {
		logID = s.aiLogService.CreateLogEntry()
	}
	logCreateDuration := time.Since(logCreateStart)
	fmt.Printf("📝 [Qwen性能分析] 创建日志条目: %v\n", logCreateDuration)

	// 获取模型配置
	configStart := time.Now()
	fmt.Printf("🔧 [Qwen性能分析] 开始获取模型配置\n")
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("获取Qwen-VL模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Qwen-VL模型未配置或未启用")
	}
	configDuration := time.Since(configStart)
	fmt.Printf("✅ [Qwen性能分析] 获取模型配置完成: %v\n", configDuration)

	// 构建请求参数
	paramsStart := time.Now()
	fmt.Printf("⚙️ [Qwen性能分析] 开始解析模型参数\n")
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}
	paramsDuration := time.Since(paramsStart)
	fmt.Printf("✅ [Qwen性能分析] 模型参数解析完成: %v\n", paramsDuration)

	// 从参数中获取动态配置的消息内容
	systemMessage := "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}"
	userMessage := "question_text内的值不应该出现题目类型以及问题序号。"

	// 如果参数中有自定义消息，则使用自定义消息
	if sysMsg, exists := params["system_message"]; exists {
		if sysStr, ok := sysMsg.(string); ok && sysStr != "" {
			systemMessage = sysStr
		}
	}
	if usrMsg, exists := params["user_message"]; exists {
		if usrStr, ok := usrMsg.(string); ok && usrStr != "" {
			userMessage = usrStr
		}
	}

	// 获取模型名称
	modelName := "qwen-vl-plus" // 默认值
	if model, exists := params["model"]; exists {
		if modelStr, ok := model.(string); ok && modelStr != "" {
			modelName = modelStr
		}
	}

	// 添加Qwen API支持的技术约束参数（放在parameters对象中）
	// 严格按照Qwen API文档，只添加支持的技术参数
	parameters := make(map[string]interface{})
	supportedParams := []string{
		"temperature", "top_p", "top_k", "do_sample",
		"response_format", "frequency_penalty", "presence_penalty",
		"result_format",
	}

	for _, paramName := range supportedParams {
		if value, exists := params[paramName]; exists {
			parameters[paramName] = value
		}
	}

	// 确保result_format为message（DashScope格式要求）
	parameters["result_format"] = "message"

	// 定义消息结构体以确保字段顺序
	type MessageContent struct {
		Image string `json:"image,omitempty"`
		Text  string `json:"text,omitempty"`
	}

	type Message struct {
		Role    string      `json:"role"`
		Content interface{} `json:"content"`
	}

	type InputData struct {
		Messages []Message `json:"messages"`
	}

	type RequestBody struct {
		Model      string      `json:"model"`
		Parameters interface{} `json:"parameters"`
		Input      InputData   `json:"input"`
	}

	// 构建符合Qwen API DashScope格式的请求体
	// 按照要求的顺序：1.model 2.parameters 3.input.messages
	requestBody := RequestBody{
		Model:      modelName,
		Parameters: parameters,
		Input: InputData{
			Messages: []Message{
				{
					Role:    "system",
					Content: systemMessage, // 取数据库内配置的system_message
				},
				{
					Role: "user",
					Content: []MessageContent{
						{
							Image: imageURL, // 取用户在业务中提交的图片url
						},
						{
							Text: userMessage, // 取数据库内配置的user_message
						},
					},
				},
			},
		},
	}

	// 记录请求数据到日志
	logRequestStart := time.Now()
	if s.aiLogService != nil {
		// 直接使用结构体保持字段顺序
		s.aiLogService.LogQwenRequest(logID, requestBody)
	}
	logRequestDuration := time.Since(logRequestStart)
	fmt.Printf("📝 [Qwen性能分析] 记录请求日志: %v\n", logRequestDuration)

	// 打印发送给Qwen的原始请求数据用于调试
	printStart := time.Now()
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给Qwen的原始请求数据:\n%s\n", string(requestBytes))
	printDuration := time.Since(printStart)
	fmt.Printf("🖨️ [Qwen性能分析] 打印请求数据: %v\n", printDuration)

	// 临时硬编码正确的DashScope多模态API URL进行测试
	correctAPIURL := "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

	// 发送请求
	httpStart := time.Now()
	fmt.Printf("🌐 [Qwen性能分析] 开始发送HTTP请求到: %s\n", correctAPIURL)
	response, err := s.sendRequest(correctAPIURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Qwen-VL模型失败: %w", err)
	}
	httpDuration := time.Since(httpStart)
	fmt.Printf("✅ [Qwen性能分析] HTTP请求完成: %v\n", httpDuration)

	// 解析响应
	parseStart := time.Now()
	fmt.Printf("🔄 [Qwen性能分析] 开始解析JSON响应\n")
	var qwenResponse model.QwenVLResponse
	if err := json.Unmarshal(response, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析Qwen-VL响应失败: %w", err)
	}
	parseDuration := time.Since(parseStart)
	fmt.Printf("✅ [Qwen性能分析] JSON响应解析完成: %v\n", parseDuration)

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("Qwen-VL模型返回空结果")
	}

	// 打印Qwen原始content数据用于调试
	printContentStart := time.Now()
	rawContent := qwenResponse.Output.Choices[0].Message.Content
	contentBytes, _ := json.Marshal(rawContent)
	fmt.Printf("🔍 Qwen原始Content数据: %s\n", string(contentBytes))
	printContentDuration := time.Since(printContentStart)
	fmt.Printf("🖨️ [Qwen性能分析] 打印原始Content: %v\n", printContentDuration)

	// 使用新的方法提取内容字符串
	extractStart := time.Now()
	fmt.Printf("📤 [Qwen性能分析] 开始提取内容字符串\n")
	content := qwenResponse.GetContentString()
	extractDuration := time.Since(extractStart)
	fmt.Printf("✅ [Qwen性能分析] 内容字符串提取完成: %v\n", extractDuration)

	// 记录响应数据到日志
	logResponseStart := time.Now()
	if s.aiLogService != nil {
		s.aiLogService.LogQwenResponse(logID, content)
	}
	logResponseDuration := time.Since(logResponseStart)
	fmt.Printf("📝 [Qwen性能分析] 记录响应日志: %v\n", logResponseDuration)

	if content == "" {
		return nil, fmt.Errorf("Qwen-VL模型返回空内容")
	}

	// 尝试解析JSON格式的响应
	structureStart := time.Now()
	fmt.Printf("🔄 [Qwen性能分析] 开始解析响应结构\n")
	structure, err := s.parseQwenResponse(content)
	if err != nil {
		return nil, fmt.Errorf("解析Qwen响应失败: %w", err)
	}
	structureDuration := time.Since(structureStart)
	fmt.Printf("✅ [Qwen性能分析] 响应结构解析完成: %v\n", structureDuration)

	totalDuration := time.Since(totalStartTime)
	fmt.Printf("🏁 [Qwen性能分析] CallQwenVL总耗时: %v\n", totalDuration)

	return &QwenVLResult{
		Structure:  &structure,
		RawContent: content,
		LogID:      logID,
	}, nil
}

// CallDeepseek 调用Deepseek模型进行题目解析
func (s *AIService) CallDeepseek(qwenResult *QwenVLResult) (*DeepseekResult, error) {
	return s.CallDeepseekWithLogID(qwenResult, "")
}

// CallDeepseekWithLogID 调用Deepseek模型进行题目解析（带日志ID）
func (s *AIService) CallDeepseekWithLogID(qwenResult *QwenVLResult, logID string) (*DeepseekResult, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameDeepseekChat)
	if err != nil {
		return nil, fmt.Errorf("获取Deepseek模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Deepseek模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 获取配置的消息内容
	systemMessage, _ := params["system_message"].(string)
	if systemMessage == "" {
		systemMessage = "你是一个专业的题目解析助手，请对提供的题目进行详细解析。"
	}

	userMessage, _ := params["user_message"].(string)
	if userMessage == "" {
		userMessage = "请以JSON格式返回，包含analysis（详细解题思路）和answer（最终答案）字段。"
	}

	// 构建完整的用户消息，只使用Qwen的原始响应内容，避免重复
	// qwenResult.RawContent 已经包含了完整的JSON数据，无需重复添加Structure
	fullUserMessage := fmt.Sprintf(`%s

以下是图像识别系统提取的题目信息：
%s

请基于以上信息进行详细的题目解析。`, userMessage, qwenResult.RawContent)

	// 构建标准OpenAI格式的请求体
	requestBody := DeepSeekRequest{
		Model: modelConfig.Name,
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemMessage,
			},
			{
				Role:    "user",
				Content: fullUserMessage,
			},
		},
	}

	// 严格按照数据库配置的参数执行，只添加存在于配置中的参数
	fmt.Printf("🔍 DeepSeek配置参数: %+v\n", params)

	// 支持的DeepSeek API参数列表
	supportedParams := map[string]bool{
		"temperature":       true,
		"top_p":             true,
		"top_k":             true,
		"max_tokens":        true,
		"do_sample":         true,
		"response_format":   true,
		"frequency_penalty": true,
		"presence_penalty":  true,
		"stream":            true,
		"stop":              true,
	}

	// 只处理数据库中配置的且API支持的参数
	if temp, exists := params["temperature"]; exists && supportedParams["temperature"] {
		if tempFloat, ok := temp.(float64); ok {
			requestBody.Temperature = &tempFloat
			fmt.Printf("✅ 添加temperature参数: %f\n", tempFloat)
		}
	}

	if topP, exists := params["top_p"]; exists && supportedParams["top_p"] {
		if topPFloat, ok := topP.(float64); ok {
			requestBody.TopP = &topPFloat
			fmt.Printf("✅ 添加top_p参数: %f\n", topPFloat)
		}
	}

	if topK, exists := params["top_k"]; exists && supportedParams["top_k"] {
		if topKFloat, ok := topK.(float64); ok {
			topKInt := int(topKFloat)
			requestBody.TopK = &topKInt
			fmt.Printf("✅ 添加top_k参数: %d\n", topKInt)
		} else if topKInt, ok := topK.(int); ok {
			requestBody.TopK = &topKInt
			fmt.Printf("✅ 添加top_k参数: %d\n", topKInt)
		}
	}

	if maxTokens, exists := params["max_tokens"]; exists && supportedParams["max_tokens"] {
		if maxTokensFloat, ok := maxTokens.(float64); ok {
			maxTokensInt := int(maxTokensFloat)
			requestBody.MaxTokens = &maxTokensInt
			fmt.Printf("✅ 添加max_tokens参数: %d\n", maxTokensInt)
		} else if maxTokensInt, ok := maxTokens.(int); ok {
			requestBody.MaxTokens = &maxTokensInt
			fmt.Printf("✅ 添加max_tokens参数: %d\n", maxTokensInt)
		}
	}

	if doSample, exists := params["do_sample"]; exists && supportedParams["do_sample"] {
		if doSampleBool, ok := doSample.(bool); ok {
			requestBody.DoSample = &doSampleBool
			fmt.Printf("✅ 添加do_sample参数: %v\n", doSampleBool)
		}
	}

	// 处理response_format参数
	if responseFormat, exists := params["response_format"]; exists && supportedParams["response_format"] {
		if responseFormatMap, ok := responseFormat.(map[string]interface{}); ok {
			if formatType, typeExists := responseFormatMap["type"].(string); typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
				fmt.Printf("✅ 添加response_format参数: %s\n", formatType)
			}
		} else if responseFormatMap, ok := responseFormat.(map[string]string); ok {
			if formatType, typeExists := responseFormatMap["type"]; typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
				fmt.Printf("✅ 添加response_format参数: %s\n", formatType)
			}
		}
	}

	// 只有在数据库配置中存在时才添加惩罚参数
	if freqPenalty, exists := params["frequency_penalty"]; exists && supportedParams["frequency_penalty"] {
		if freqPenaltyFloat, ok := freqPenalty.(float64); ok {
			requestBody.FrequencyPenalty = &freqPenaltyFloat
			fmt.Printf("✅ 添加frequency_penalty参数: %f\n", freqPenaltyFloat)
		}
	}

	if presPenalty, exists := params["presence_penalty"]; exists && supportedParams["presence_penalty"] {
		if presPenaltyFloat, ok := presPenalty.(float64); ok {
			requestBody.PresencePenalty = &presPenaltyFloat
			fmt.Printf("✅ 添加presence_penalty参数: %f\n", presPenaltyFloat)
		}
	}

	// 处理stream参数
	if stream, exists := params["stream"]; exists && supportedParams["stream"] {
		if streamBool, ok := stream.(bool); ok {
			requestBody.Stream = &streamBool
			fmt.Printf("✅ 添加stream参数: %v\n", streamBool)
		}
	}

	// 处理stop参数
	if stop, exists := params["stop"]; exists && supportedParams["stop"] {
		requestBody.Stop = stop
		fmt.Printf("✅ 添加stop参数: %+v\n", stop)
	}

	// 记录请求数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekRequest(logID, requestBody)
	}

	// 打印发送给DeepSeek的原始请求数据用于调试
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给DeepSeek的原始请求数据:\n%s\n", string(requestBytes))

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Deepseek模型失败: %w", err)
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(response, &deepseekResponse); err != nil {
		return nil, fmt.Errorf("解析Deepseek响应失败: %w", err)
	}

	if len(deepseekResponse.Choices) == 0 {
		return nil, fmt.Errorf("Deepseek模型返回空结果")
	}

	content := deepseekResponse.Choices[0].Message.Content

	// 打印DeepSeek原始Content数据用于调试
	fmt.Printf("🔍 DeepSeek原始Content数据: %s\n", content)

	// 记录响应数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekResponse(logID, content)
	}

	// 简化验证：只做基础检查，避免冗余验证
	fmt.Printf("🔍 [调试] 开始基础验证DeepSeek响应内容\n")
	if strings.TrimSpace(content) == "" {
		return nil, fmt.Errorf("DeepSeek响应内容为空")
	}
	if len(strings.TrimSpace(content)) < 5 {
		return nil, fmt.Errorf("DeepSeek响应内容过短: %s", content)
	}
	fmt.Printf("✅ [调试] 基础验证通过\n")

	// 尝试解析JSON格式的响应
	var rawResponse map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawResponse); err != nil {
		fmt.Printf("⚠️ [调试] JSON解析失败，作为纯文本处理: %v\n", err)
		// 如果不是JSON格式，作为纯文本处理
		if len(strings.TrimSpace(content)) < 10 {
			return nil, fmt.Errorf("DeepSeek返回内容过短，可能是错误响应: %s", content)
		}

		// 作为纯文本处理
		return &DeepseekResult{
			Analysis:   content,
			Answer:     "请参考解析内容",
			RawContent: content,
		}, nil
	}

	fmt.Printf("✅ [调试] JSON解析成功，开始字段提取\n")
	// 灵活解析DeepSeek响应，支持多种字段格式
	result := s.parseDeepSeekResponse(rawResponse)

	// 最终验证：确保至少有一些有用的内容
	if strings.TrimSpace(result.Analysis) == "" && strings.TrimSpace(result.Answer) == "" {
		fmt.Printf("⚠️ [调试] 解析结果为空，使用原始内容作为分析\n")
		result.Analysis = content
		result.Answer = "请参考解析内容"
	}

	result.RawContent = content
	fmt.Printf("✅ [调试] DeepSeek响应处理完成\n")
	return result, nil
}

// sendRequest 发送HTTP请求
func (s *AIService) sendRequest(apiURL, apiKey string, requestBody interface{}) ([]byte, error) {
	requestStartTime := time.Now()
	fmt.Printf("🌐 [HTTP性能分析] 开始sendRequest - URL: %s\n", apiURL)

	// 序列化请求体
	marshalStart := time.Now()
	fmt.Printf("📦 [HTTP性能分析] 开始序列化请求体\n")
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}
	marshalDuration := time.Since(marshalStart)
	fmt.Printf("✅ [HTTP性能分析] 请求体序列化完成: %v, 大小: %d bytes\n", marshalDuration, len(jsonData))

	// 创建HTTP请求
	createReqStart := time.Now()
	fmt.Printf("🔧 [HTTP性能分析] 开始创建HTTP请求\n")
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	createReqDuration := time.Since(createReqStart)
	fmt.Printf("✅ [HTTP性能分析] HTTP请求创建完成: %v\n", createReqDuration)

	// 设置请求头
	headerStart := time.Now()
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	headerDuration := time.Since(headerStart)
	fmt.Printf("🏷️ [HTTP性能分析] 请求头设置完成: %v\n", headerDuration)

	// 发送请求
	clientStart := time.Now()
	fmt.Printf("🚀 [HTTP性能分析] 开始发送HTTP请求\n")
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ HTTP请求发送失败: %v\n", err)
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()
	clientDuration := time.Since(clientStart)
	fmt.Printf("✅ [HTTP性能分析] HTTP请求发送完成: %v\n", clientDuration)

	// 读取响应
	readStart := time.Now()
	fmt.Printf("📥 [HTTP性能分析] 开始读取响应体\n")
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}
	readDuration := time.Since(readStart)
	fmt.Printf("✅ [HTTP性能分析] 响应体读取完成: %v, 大小: %d bytes\n", readDuration, len(body))

	// 打印详细的响应信息用于调试
	printStart := time.Now()
	fmt.Printf("📋 API响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("📋 API响应头: %+v\n", resp.Header)
	fmt.Printf("📋 API响应内容: %s\n", string(body))
	printDuration := time.Since(printStart)
	fmt.Printf("🖨️ [HTTP性能分析] 响应信息打印完成: %v\n", printDuration)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ API请求失败，状态码: %d, 响应: %s\n", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	totalRequestDuration := time.Since(requestStartTime)
	fmt.Printf("🏁 [HTTP性能分析] sendRequest总耗时: %v\n", totalRequestDuration)

	return body, nil
}

// validateDeepSeekResponse 简化的DeepSeek响应验证（仅保留必要的基础检查）
func (s *AIService) validateDeepSeekResponse(content string) error {
	// 只做最基础的检查，避免过度验证
	if strings.TrimSpace(content) == "" {
		return fmt.Errorf("响应内容为空")
	}
	if len(strings.TrimSpace(content)) < 5 {
		return fmt.Errorf("响应内容过短: %s", content)
	}
	return nil
}

// ValidateImageURL 验证图片URL是否可访问
func (s *AIService) ValidateImageURL(imageURL string) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return fmt.Errorf("无法访问图片URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("无法确定文件类型")
	}

	validTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的图片格式: %s", contentType)
}

// parseQwenResponse 解析Qwen响应，支持中文和英文字段名
func (s *AIService) parseQwenResponse(content string) (model.PreprocessedQuestion, error) {
	var structure model.PreprocessedQuestion

	// 解析原始JSON数据
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawData); err != nil {
		// 如果JSON解析完全失败，作为纯文本处理
		fmt.Printf("⚠️ JSON解析失败，作为纯文本处理\n")
		return model.PreprocessedQuestion{
			QuestionText: content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3, // 默认中等难度
		}, nil
	}

	fmt.Printf("🔍 成功解析JSON，开始提取字段\n")

	// 提取题目类型（支持中英文字段名）
	if questionType, exists := rawData["question_type"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	} else if questionType, exists := rawData["类型"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	}

	// 提取题目内容（支持中英文字段名）
	if questionText, exists := rawData["question_text"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	} else if questionText, exists := rawData["题目"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	}

	// 提取选项到分散字段
	if option, exists := rawData["A"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsA = optionStr
			fmt.Printf("✅ 提取选项 A='%s'\n", optionStr)
		}
	}
	if option, exists := rawData["B"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsB = optionStr
			fmt.Printf("✅ 提取选项 B='%s'\n", optionStr)
		}
	}
	if option, exists := rawData["C"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsC = optionStr
			fmt.Printf("✅ 提取选项 C='%s'\n", optionStr)
		}
	}
	if option, exists := rawData["D"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsD = optionStr
			fmt.Printf("✅ 提取选项 D='%s'\n", optionStr)
		}
	}
	if option, exists := rawData["Y"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsY = optionStr
			fmt.Printf("✅ 提取选项 Y='%s'\n", optionStr)
		}
	}
	if option, exists := rawData["N"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsN = optionStr
			fmt.Printf("✅ 提取选项 N='%s'\n", optionStr)
		}
	}

	optionCount := 0
	if structure.OptionsA != "" {
		optionCount++
	}
	if structure.OptionsB != "" {
		optionCount++
	}
	if structure.OptionsC != "" {
		optionCount++
	}
	if structure.OptionsD != "" {
		optionCount++
	}
	if structure.OptionsY != "" {
		optionCount++
	}
	if structure.OptionsN != "" {
		optionCount++
	}
	fmt.Printf("🔍 提取到选项数量: %d\n", optionCount)

	// 检查是否解析到有效数据
	if structure.QuestionText == "" && structure.QuestionType == "" {
		fmt.Printf("⚠️ 未解析到有效的题目数据\n")
		return model.PreprocessedQuestion{
			QuestionText: content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3,
		}, nil
	}

	// 保持原始的Y/N选项格式，不进行A/B映射
	if strings.Contains(strings.ToLower(structure.QuestionType), "判断") {
		fmt.Printf("🔍 检测到判断题，保持Y/N选项格式\n")
	}

	// 设置默认值
	if structure.Subject == "" {
		structure.Subject = "未知"
	}
	if structure.Grade == "" {
		structure.Grade = "未知"
	}
	if structure.Difficulty == 0 {
		structure.Difficulty = 3 // 默认中等难度
	}

	fmt.Printf("✅ 成功解析Qwen响应\n")
	fmt.Printf("🔍 解析结果 - 类型: %s, 题目: %s\n", structure.QuestionType, structure.QuestionText)

	return structure, nil
}

// parseDeepSeekResponse 灵活的DeepSeek响应解析，支持多种字段格式
func (s *AIService) parseDeepSeekResponse(rawResponse map[string]interface{}) *DeepseekResult {
	result := &DeepseekResult{}

	fmt.Printf("🔍 [解析] 开始解析DeepSeek响应，字段数量: %d\n", len(rawResponse))

	// 提取分析字段（支持多种字段名）
	analysisFields := []string{"analysis", "解析", "分析", "explanation"}
	for _, field := range analysisFields {
		if analysis, exists := rawResponse[field]; exists {
			if str, ok := analysis.(string); ok && strings.TrimSpace(str) != "" {
				result.Analysis = str
				fmt.Printf("✅ 提取%s字段: %s\n", field, str[:min(50, len(str))])
				break
			}
		}
	}

	// 直接提取answer字段，支持多选题JSON格式
	result.Answer = s.extractAnswerFromResponse(rawResponse)

	// 直接提取选项信息
	result.Options = s.extractOptionsFromResponse(rawResponse)

	// 直接提取题目类型
	if questionType, exists := rawResponse["type"]; exists {
		if str, ok := questionType.(string); ok && strings.TrimSpace(str) != "" {
			result.QuestionType = str
			fmt.Printf("✅ 提取type字段: %s\n", str)
		}
	}

	fmt.Printf("🔍 DeepSeek响应解析完成 - 分析长度: %d, 答案: %s, 题目类型: %s, 选项数量: %d\n",
		len(result.Analysis), result.Answer, result.QuestionType, len(result.Options))
	return result
}

// extractAnswerFromResponse 从响应中提取答案（支持S1样本格式和实际DeepSeek格式）
func (s *AIService) extractAnswerFromResponse(rawResponse map[string]interface{}) string {
	// 1. 尝试提取标准answer字段
	answerFields := []string{"answer", "答案", "正确答案", "result"}
	for _, field := range answerFields {
		if value, exists := rawResponse[field]; exists {
			// 处理字符串格式的答案
			if str, ok := value.(string); ok && strings.TrimSpace(str) != "" {
				fmt.Printf("✅ 提取字符串答案字段 '%s': %s\n", field, str)

				// 处理DeepSeek实际返回的格式（如"A/Y"）
				if strings.Contains(str, "/") {
					// 对于判断题，"A/Y"表示正确，"B/N"表示错误
					if str == "A/Y" {
						fmt.Printf("✅ 标准化答案格式: %s -> Y\n", str)
						return "Y"
					} else if str == "B/N" {
						fmt.Printf("✅ 标准化答案格式: %s -> N\n", str)
						return "N"
					}
					// 其他格式保持原样
					fmt.Printf("✅ 保持原始答案格式: %s\n", str)
					return str
				}

				return str
			}

			// 处理对象格式的答案（多选题可能返回对象）
			if answerObj, ok := value.(map[string]interface{}); ok {
				var answers []string
				// 按顺序检查A、B、C、D、Y、N选项
				for _, option := range []string{"A", "B", "C", "D", "Y", "N"} {
					if _, hasOption := answerObj[option]; hasOption {
						// 只要存在这个键就认为是答案
						answers = append(answers, option)
						fmt.Printf("✅ 从对象中提取答案选项: %s\n", option)
					}
				}
				if len(answers) > 0 {
					// 多选题返回JSON格式，单选题返回字符串
					if len(answers) > 1 {
						jsonBytes, _ := json.Marshal(answers)
						result := string(jsonBytes)
						fmt.Printf("✅ 多选题答案(JSON格式): %s\n", result)
						return result
					} else {
						// 单个答案直接返回字符串
						fmt.Printf("✅ 单选题答案: %s\n", answers[0])
						return answers[0]
					}
				}
			}
		}
	}

	fmt.Printf("⚠️ 未能从answer字段提取到有效答案\n")
	return ""
}

// extractOptionsFromResponse 简化的选项提取，直接从DeepSeek响应提取
func (s *AIService) extractOptionsFromResponse(rawResponse map[string]interface{}) map[string]string {
	options := make(map[string]string)

	// 直接从根级别提取选项，处理DeepSeek实际返回格式
	optionKeys := []string{"A", "B", "C", "D", "Y", "N", "A/Y", "B/N"}
	for _, key := range optionKeys {
		if value, exists := rawResponse[key]; exists {
			if str, ok := value.(string); ok && strings.TrimSpace(str) != "" {
				// 处理DeepSeek的A/Y、B/N格式
				if key == "A/Y" {
					options["Y"] = str
					fmt.Printf("✅ 提取选项 Y='%s' (来源: A/Y)\n", str)
				} else if key == "B/N" {
					options["N"] = str
					fmt.Printf("✅ 提取选项 N='%s' (来源: B/N)\n", str)
				} else {
					// 标准选项直接存储
					options[key] = str
					fmt.Printf("✅ 提取选项 %s='%s'\n", key, str)
				}
			}
		}
	}

	fmt.Printf("✅ 提取选项完成: %v\n", options)
	return options
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
