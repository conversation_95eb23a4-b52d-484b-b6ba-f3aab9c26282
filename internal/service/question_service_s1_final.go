package service

import (
	"fmt"
	"time"

	"solve_api/internal/model"
	"solve_api/internal/repository"
)

// QuestionServiceS1Final 严格按照S1.md要求实现的最终版本
// S1.md要求：
// 1. qwen返回数据进行格式化解析
// 2. 将格式化解析得到的内容进行哈希
// 3. 使用哈希查找redis的缓存键是否匹配
//    3.1 redis存在---返回对应的valve；业务结束
//    3.2 redis不存在---查询mysql
//        3.2.1 mysql存在---回写redis，返回对应的valve；业务结束
//        3.2.2 mysql不存在---调用deepseek，将结果保存到mysql和redis，返回结果；业务结束
// 4. deepseek的返回的数据不应该直接生成valve，应该先按照逻辑存入mysql后，在从mysql中按照valve的生成方式进行回写redis
type QuestionServiceS1Final struct {
	questionCacheRepo *repository.QuestionCacheRepository
	questionRepo      *repository.QuestionRepository
	userRepo          *repository.UserRepository
	balanceLogRepo    *repository.BalanceLogRepository
	aiService         *AIService
}

// NewQuestionServiceS1Final 创建严格按照S1.md要求的服务
func NewQuestionServiceS1Final(
	questionCacheRepo *repository.QuestionCacheRepository,
	questionRepo *repository.QuestionRepository,
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
	aiService *AIService,
) *QuestionServiceS1Final {
	return &QuestionServiceS1Final{
		questionCacheRepo: questionCacheRepo,
		questionRepo:      questionRepo,
		userRepo:          userRepo,
		balanceLogRepo:    balanceLogRepo,
		aiService:         aiService,
	}
}

// Search 严格按照S1.md要求实现的拍照搜题流程
func (s *QuestionServiceS1Final) Search(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()
	fmt.Printf("🚀 [S1流程] 开始拍照搜题 - 用户ID: %d, 图片URL: %s\n", userID, req.ImageURL)

	// 验证图片URL
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}

	// 1. qwen返回数据进行格式化解析
	fmt.Printf("📋 [S1流程] 步骤1: Qwen返回数据进行格式化解析\n")
	qwenResult, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}

	preprocessed, err := model.PreprocessQwenResult(qwenResult.Structure)
	if err != nil || !preprocessed.IsValid {
		return nil, fmt.Errorf(preprocessed.ErrorMessage)
	}
	fmt.Printf("✅ [S1流程] 步骤1完成: 格式化解析成功\n")

	// 2. 将格式化解析得到的内容进行哈希
	fmt.Printf("🔑 [S1流程] 步骤2: 将格式化解析得到的内容进行哈希\n")
	cacheKey := model.GenerateCacheKeyFromPreprocessed(preprocessed)
	fmt.Printf("✅ [S1流程] 步骤2完成: 哈希生成 - %s\n", cacheKey)

	// 3. 使用哈希查找redis的缓存键是否匹配
	fmt.Printf("🔍 [S1流程] 步骤3: 使用哈希查找Redis缓存键\n")
	redisQuestions, err := s.questionCacheRepo.GetWithAssociates(cacheKey)
	
	// 3.1 redis存在---返回对应的valve；业务结束
	if err == nil && len(redisQuestions) > 0 {
		fmt.Printf("✅ [S1流程] 步骤3.1: Redis存在，返回对应的valve，业务结束\n")
		go s.questionRepo.IncrementResponse(redisQuestions[0].ID) // 异步更新响应次数
		processTime := time.Since(startTime).Milliseconds()
		return redisQuestions[0].ToSearchResponse(true, processTime), nil
	}
	fmt.Printf("❌ [S1流程] 步骤3.1: Redis不存在\n")

	// 3.2 redis不存在---查询mysql
	fmt.Printf("🗄️ [S1流程] 步骤3.2: Redis不存在，查询MySQL\n")
	mysqlQuestions, err := s.questionRepo.GetWithAssociates(cacheKey)
	
	// 3.2.1 mysql存在---回写redis，返回对应的valve；业务结束
	if err == nil && len(mysqlQuestions) > 0 {
		fmt.Printf("✅ [S1流程] 步骤3.2.1: MySQL存在，回写Redis，返回对应的valve，业务结束\n")
		
		// 回写Redis
		for _, q := range mysqlQuestions {
			if err := s.questionCacheRepo.Set(q.CacheKey, q); err != nil {
				fmt.Printf("⚠️ [S1流程] Redis回写失败: %v\n", err)
			}
		}
		
		go s.questionRepo.IncrementResponse(mysqlQuestions[0].ID) // 异步更新响应次数
		processTime := time.Since(startTime).Milliseconds()
		return mysqlQuestions[0].ToSearchResponse(true, processTime), nil
	}
	fmt.Printf("❌ [S1流程] 步骤3.2.1: MySQL不存在\n")

	// 3.2.2 mysql不存在---调用deepseek，将结果保存到mysql和redis，返回结果；业务结束
	fmt.Printf("🧠 [S1流程] 步骤3.2.2: MySQL不存在，调用DeepSeek\n")
	deepseekResult, err := s.aiService.CallDeepseekWithLogID(qwenResult, qwenResult.LogID)
	if err != nil {
		return nil, fmt.Errorf("题目解析失败: %w", err)
	}

	// 4. 重要：deepseek的返回的数据不应该直接生成valve，应该先按照逻辑存入mysql后，在从mysql中按照valve的生成方式进行回写redis
	fmt.Printf("💾 [S1流程] 步骤4: 先存入MySQL，再从MySQL回写Redis\n")
	
	// 4.1 创建题目并保存到MySQL
	newQuestion := &model.Question{
		SourceModel: "qwen-vl-plus,deepseek-chat",
	}
	newQuestion.FromPreprocessedWithDeepSeek(preprocessed, req.ImageURL, qwenResult.RawContent, deepseekResult)
	
	if err := s.questionRepo.Create(newQuestion); err != nil {
		return nil, fmt.Errorf("保存题目到MySQL失败: %w", err)
	}
	fmt.Printf("✅ [S1流程] 步骤4.1: 题目已保存到MySQL - ID: %d\n", newQuestion.ID)

	// 4.2 从MySQL中按照valve的生成方式进行回写Redis
	savedQuestion, err := s.questionRepo.GetByID(newQuestion.ID)
	if err != nil {
		return nil, fmt.Errorf("从MySQL获取保存的题目失败: %w", err)
	}
	
	if err := s.questionCacheRepo.Set(savedQuestion.CacheKey, savedQuestion); err != nil {
		fmt.Printf("⚠️ [S1流程] Redis回写失败: %v\n", err)
		// Redis失败不影响主流程，因为数据已经保存到MySQL
	}
	fmt.Printf("✅ [S1流程] 步骤4.2: 从MySQL回写Redis完成\n")

	// 扣费处理
	if err := s.processPayment(userID, model.ApplicationTypePhotoSearch); err != nil {
		fmt.Printf("⚠️ [S1流程] 扣费失败: %v\n", err)
	}

	// 构建响应
	processTime := time.Since(startTime).Milliseconds()
	response := savedQuestion.ToSearchResponse(false, processTime)
	response.QwenRawContent = qwenResult.RawContent
	response.DeepseekRawContent = deepseekResult.RawContent

	fmt.Printf("🏁 [S1流程] 业务结束，总耗时: %v\n", time.Since(startTime))
	return response, nil
}

// processPayment 处理扣费
func (s *QuestionServiceS1Final) processPayment(userID uint, serviceType int) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 获取服务价格（这里简化处理，使用固定价格）
	price := 0.01 // 默认0.01元/次

	// 检查余额
	if user.Balance < price {
		return fmt.Errorf("余额不足")
	}

	// 扣费
	user.Balance -= price
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("扣费失败: %w", err)
	}

	// 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -price,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: "拍照搜题服务费用",
		RelatedID:   0, // 可以关联题目ID
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 日志记录失败不影响主流程
		return nil
	}

	return nil
}

// GetByID 根据ID获取题目
func (s *QuestionServiceS1Final) GetByID(id uint) (*model.QuestionResponse, error) {
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %w", err)
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	return question.ToResponse(), nil
}

// GetList 获取题目列表
func (s *QuestionServiceS1Final) GetList(page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// SearchQuestions 搜索题目
func (s *QuestionServiceS1Final) SearchQuestions(keyword string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.Search(keyword, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索题目失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// CreateManagementQuestion 创建题库管理题目
func (s *QuestionServiceS1Final) CreateManagementQuestion(req *model.QuestionManagementRequest) (*model.Question, error) {
	question, err := s.questionRepo.CreateManagementQuestion(req)
	if err != nil {
		return nil, err
	}

	// 同步到Redis缓存
	go s.questionCacheRepo.Set(question.CacheKey, question)

	return question, nil
}

// UpdateManagementQuestion 更新题库管理题目
func (s *QuestionServiceS1Final) UpdateManagementQuestion(id uint, req *model.QuestionUpdateRequest) (*model.Question, error) {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.QuestionType != nil {
		updates["question_type"] = *req.QuestionType
	}
	if req.QuestionText != nil {
		updates["question_text"] = *req.QuestionText
	}
	if req.QuestionImgRaw != nil {
		updates["question_img_raw"] = *req.QuestionImgRaw
	}
	if req.OptionsA != nil {
		updates["options_a"] = *req.OptionsA
	}
	if req.OptionsB != nil {
		updates["options_b"] = *req.OptionsB
	}
	if req.OptionsC != nil {
		updates["options_c"] = *req.OptionsC
	}
	if req.OptionsD != nil {
		updates["options_d"] = *req.OptionsD
	}
	if req.OptionsY != nil {
		updates["options_y"] = *req.OptionsY
	}
	if req.OptionsN != nil {
		updates["options_n"] = *req.OptionsN
	}
	if req.Answer != nil {
		updates["answer"] = *req.Answer
	}
	if req.Analysis != nil {
		updates["analysis"] = *req.Analysis
	}

	// 执行更新
	if err := s.questionRepo.UpdateQuestion(id, updates); err != nil {
		return nil, fmt.Errorf("更新题目失败: %w", err)
	}

	// 获取更新后的题目
	updatedQuestion, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 同步到Redis缓存
	go s.questionCacheRepo.Set(updatedQuestion.CacheKey, updatedQuestion)

	return updatedQuestion, nil
}

// DeleteQuestion 删除题目
func (s *QuestionServiceS1Final) DeleteQuestion(id uint) error {
	// 获取题目信息
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return err
	}
	if question == nil {
		return fmt.Errorf("题目不存在")
	}

	// 从MySQL删除（软删除）
	if err := s.questionRepo.Delete(id); err != nil {
		return err
	}

	// 从Redis删除
	go s.questionCacheRepo.Delete(question.CacheKey)

	return nil
}

// GetStats 获取题目统计信息
func (s *QuestionServiceS1Final) GetStats() (map[string]interface{}, error) {
	// 获取总数
	totalCount, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %w", err)
	}

	// 获取各学科统计
	subjectCount, err := s.questionRepo.GetCountBySubject()
	if err != nil {
		return nil, fmt.Errorf("获取学科统计失败: %w", err)
	}

	// 获取各年级统计
	gradeCount, err := s.questionRepo.GetCountByGrade()
	if err != nil {
		return nil, fmt.Errorf("获取年级统计失败: %w", err)
	}

	// 获取各难度统计
	difficultyCount, err := s.questionRepo.GetCountByDifficulty()
	if err != nil {
		return nil, fmt.Errorf("获取难度统计失败: %w", err)
	}

	// 获取缓存统计
	cacheStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		cacheStats = map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}
	}

	return map[string]interface{}{
		"total_questions":  totalCount,
		"subject_count":    subjectCount,
		"grade_count":      gradeCount,
		"difficulty_count": difficultyCount,
		"cache_stats":      cacheStats,
	}, nil
}

// ClearCache 清空题目缓存
func (s *QuestionServiceS1Final) ClearCache() error {
	return s.questionCacheRepo.Clear()
}
