package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelInfo LogLevel = iota
	LogLevelWarn
	LogLevelError
	LogLevelDebug
)

// UnifiedLogger 统一日志中间件 - 整合所有日志需求
func UnifiedLogger(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 创建统一的响应写入器
		responseWriter := &UnifiedResponseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = responseWriter

		// 读取请求体（如果需要记录详细日志）
		var requestBody []byte
		if shouldLogDetails(c.Request.URL.Path) && c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录日志
		logRequest(c, logger, requestBody, responseWriter.body.Bytes(), duration)

		// 异步记录API日志到数据库（如果需要）
		if shouldLogToDB(c.Request.URL.Path) {
			go logToDatabase(c, responseWriter, duration, startTime)
		}
	}
}

// UnifiedResponseWriter 统一的响应写入器
type UnifiedResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 写入响应数据
func (w *UnifiedResponseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseWriter.Write(data)
}

// WriteString 写入字符串响应数据
func (w *UnifiedResponseWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// GetBody 获取响应体内容
func (w *UnifiedResponseWriter) GetBody() []byte {
	return w.body.Bytes()
}

// logRequest 记录请求日志
func logRequest(c *gin.Context, logger *zap.Logger, requestBody, responseBody []byte, duration time.Duration) {
	path := c.Request.URL.Path
	statusCode := c.Writer.Status()

	// 基础日志字段
	fields := []zap.Field{
		zap.String("method", c.Request.Method),
		zap.String("path", path),
		zap.String("query", c.Request.URL.RawQuery),
		zap.String("ip", c.ClientIP()),
		zap.String("user_agent", c.Request.UserAgent()),
		zap.Int("status", statusCode),
		zap.Duration("latency", duration),
	}

	// 添加认证信息（如果存在）
	if authResult, exists := GetAuthResult(c); exists {
		fields = append(fields,
			zap.Uint("user_id", authResult.User.ID),
			zap.Uint("app_id", authResult.Application.ID),
			zap.String("auth_method", authResult.AuthMethod),
		)
	}

	// 添加错误信息（如果有）
	if len(c.Errors) > 0 {
		fields = append(fields, zap.String("errors", c.Errors.String()))
	}

	// 详细日志记录
	if shouldLogDetails(path) {
		// 添加请求体（脱敏处理）
		if len(requestBody) > 0 {
			if sanitizedBody := sanitizeRequestBody(requestBody); sanitizedBody != nil {
				fields = append(fields, zap.Any("request_body", sanitizedBody))
			}
		}

		// 添加响应体（如果不太大）
		if len(responseBody) > 0 && len(responseBody) < 1024 {
			if responseJSON := parseResponseBody(responseBody); responseJSON != nil {
				fields = append(fields, zap.Any("response_body", responseJSON))
			}
		}
	}

	// 根据状态码选择日志级别
	logLevel := getLogLevel(statusCode)
	logMessage := fmt.Sprintf("%s %s", c.Request.Method, path)

	switch logLevel {
	case LogLevelError:
		logger.Error(logMessage, fields...)
	case LogLevelWarn:
		logger.Warn(logMessage, fields...)
	case LogLevelDebug:
		logger.Debug(logMessage, fields...)
	default:
		logger.Info(logMessage, fields...)
	}

	// 控制台输出（格式化）
	printConsoleLog(c, duration, statusCode)
}

// logToDatabase 异步记录API日志到数据库
func logToDatabase(c *gin.Context, responseWriter *UnifiedResponseWriter, duration time.Duration, startTime time.Time) {
	db := database.GetDB()
	if db == nil {
		return
	}

	// 获取认证信息
	var userID, appID uint
	var appType int
	var servicePrice float64

	if authResult, exists := GetAuthResult(c); exists {
		userID = authResult.User.ID
		appID = authResult.Application.ID
		appType = authResult.Application.Type
		servicePrice = authResult.ServicePrice
	}

	// 计算请求和响应大小
	requestSize := c.Request.ContentLength
	if requestSize < 0 {
		requestSize = 0
	}
	responseSize := int64(responseWriter.body.Len())

	// 获取错误信息
	errorMsg := ""
	if len(c.Errors) > 0 {
		errorMsg = c.Errors.String()
	}

	// 创建API日志
	apiLog := &model.APILog{
		UserID:       userID,
		AppID:        appID,
		Method:       c.Request.Method,
		Path:         c.Request.URL.Path,
		UserAgent:    c.Request.UserAgent(),
		ClientIP:     c.ClientIP(),
		StatusCode:   c.Writer.Status(),
		ResponseTime: duration.Milliseconds(),
		RequestSize:  requestSize,
		ResponseSize: responseSize,
		ErrorMsg:     errorMsg,
		ServiceType:  appType,
		Cost:         servicePrice,
	}

	// 保存到数据库
	apiLogRepo := repository.NewAPILogRepository(db)
	if err := apiLogRepo.Create(apiLog); err != nil {
		// 日志记录失败不影响主流程，但可以记录到系统日志
		fmt.Printf("⚠️ API日志记录失败: %v\n", err)
	}
}

// shouldLogDetails 判断是否需要记录详细日志
func shouldLogDetails(path string) bool {
	detailPaths := []string{
		"/api/v1/api/search",
		"/api/v1/admin",
		"/api/v1/user",
	}

	for _, detailPath := range detailPaths {
		if strings.HasPrefix(path, detailPath) {
			return true
		}
	}
	return false
}

// shouldLogToDB 判断是否需要记录到数据库
func shouldLogToDB(path string) bool {
	// 跳过健康检查和静态资源
	skipPaths := []string{
		"/health",
		"/static",
		"/favicon.ico",
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return false
		}
	}
	return true
}

// getLogLevel 根据状态码获取日志级别
func getLogLevel(statusCode int) LogLevel {
	switch {
	case statusCode >= 500:
		return LogLevelError
	case statusCode >= 400:
		return LogLevelWarn
	case statusCode >= 300:
		return LogLevelInfo
	default:
		return LogLevelInfo
	}
}

// sanitizeRequestBody 脱敏处理请求体
func sanitizeRequestBody(body []byte) interface{} {
	var requestData map[string]interface{}
	if err := json.Unmarshal(body, &requestData); err != nil {
		return string(body) // 如果不是JSON，返回原始字符串
	}

	// 脱敏处理
	sanitized := make(map[string]interface{})
	for key, value := range requestData {
		lowerKey := strings.ToLower(key)
		switch lowerKey {
		case "password", "old_password", "new_password", "secret_key":
			sanitized[key] = "***"
		case "phone":
			if phoneStr, ok := value.(string); ok && len(phoneStr) >= 7 {
				sanitized[key] = phoneStr[:3] + "****" + phoneStr[len(phoneStr)-4:]
			} else {
				sanitized[key] = value
			}
		default:
			sanitized[key] = value
		}
	}

	return sanitized
}

// parseResponseBody 解析响应体
func parseResponseBody(body []byte) interface{} {
	var responseData interface{}
	if err := json.Unmarshal(body, &responseData); err != nil {
		return nil // 解析失败，不记录响应体
	}
	return responseData
}

// printConsoleLog 打印控制台日志
func printConsoleLog(c *gin.Context, duration time.Duration, statusCode int) {
	path := c.Request.URL.Path

	// 根据状态码选择颜色和图标
	var icon, color string
	switch {
	case statusCode >= 500:
		icon = "🔴"
		color = "\033[31m" // 红色
	case statusCode >= 400:
		icon = "🟡"
		color = "\033[33m" // 黄色
	case statusCode >= 300:
		icon = "🟠"
		color = "\033[36m" // 青色
	default:
		icon = "🟢"
		color = "\033[32m" // 绿色
	}

	reset := "\033[0m"

	// 简化的控制台输出
	if shouldLogDetails(path) {
		fmt.Printf("%s%s [%s] %s %s %d %v %s%s\n",
			color, icon,
			time.Now().Format("15:04:05"),
			c.Request.Method,
			path,
			statusCode,
			duration,
			c.ClientIP(),
			reset)
	}
}

// Recovery 统一的恢复中间件
func UnifiedRecovery(logger *zap.Logger) gin.HandlerFunc {
	return gin.RecoveryWithWriter(gin.DefaultWriter, func(c *gin.Context, recovered interface{}) {
		// 记录panic日志
		logger.Error("HTTP Panic Recovered",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("ip", c.ClientIP()),
			zap.Any("recovered", recovered),
		)

		// 返回500错误
		c.JSON(500, gin.H{
			"code":    500,
			"message": "服务器内部错误",
		})
	})
}
