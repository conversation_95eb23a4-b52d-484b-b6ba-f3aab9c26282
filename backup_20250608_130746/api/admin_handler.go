package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AdminHandler struct {
	adminService *service.AdminService
}

// NewAdminHandler 创建管理员处理器实例
func NewAdminHandler(adminService *service.AdminService) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
	}
}

// Login 管理员登录
// @Summary 管理员登录
// @Description 管理员用户名密码登录
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param request body model.AdminLoginRequest true "登录请求参数"
// @Success 200 {object} utils.Response{data=model.AdminResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "用户名或密码错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/login [post]
func (h *AdminHandler) Login(c *gin.Context) {
	var req model.AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	admin, err := h.adminService.Login(&req)
	if err != nil {
		if err.Error() == "管理员不存在" || err.Error() == "密码错误" {
			utils.Unauthorized(c, "用户名或密码错误")
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "登录成功", admin)
}

// ChangePassword 管理员修改密码
// @Summary 管理员修改密码
// @Description 管理员使用原密码修改密码
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param admin_id path uint true "管理员ID"
// @Param request body model.AdminChangePasswordRequest true "修改密码请求参数"
// @Success 200 {object} utils.Response{data=model.AdminResponse} "修改成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "原密码错误"
// @Failure 404 {object} utils.Response "管理员不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/{admin_id}/change-password [put]
func (h *AdminHandler) ChangePassword(c *gin.Context) {
	// 1. 获取管理员ID
	adminIDStr := c.Param("admin_id")
	if adminIDStr == "" {
		utils.BadRequest(c, "管理员ID不能为空")
		return
	}
	
	adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "管理员ID格式错误")
		return
	}

	// 2. 解析请求参数
	var req model.AdminChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 3. 修改密码
	admin, err := h.adminService.ChangePassword(uint(adminID), &req)
	if err != nil {
		if err.Error() == "管理员不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		if err.Error() == "原密码错误" {
			utils.Unauthorized(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "密码修改成功", admin)
}

// SendForgotPasswordCode 发送忘记密码验证码
// @Summary 发送忘记密码验证码
// @Description 管理员忘记密码时发送验证码
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param request body model.AdminForgotPasswordRequest true "发送验证码请求参数"
// @Success 200 {object} utils.Response "发送成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "管理员不存在"
// @Failure 429 {object} utils.Response "发送过于频繁"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/forgot-password [post]
func (h *AdminHandler) SendForgotPasswordCode(c *gin.Context) {
	var req model.AdminForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	err := h.adminService.SendForgotPasswordCode(&req)
	if err != nil {
		if err.Error() == "管理员不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		if err.Error() == "验证码发送过于频繁，请稍后再试" {
			utils.TooManyRequests(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "验证码发送成功", nil)
}

// ResetPassword 管理员重置密码
// @Summary 管理员重置密码
// @Description 管理员使用验证码重置密码
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param request body model.AdminResetPasswordRequest true "重置密码请求参数"
// @Success 200 {object} utils.Response{data=model.AdminResponse} "重置成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "管理员不存在"
// @Failure 401 {object} utils.Response "验证码错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/reset-password [post]
func (h *AdminHandler) ResetPassword(c *gin.Context) {
	var req model.AdminResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	admin, err := h.adminService.ResetPassword(&req)
	if err != nil {
		if err.Error() == "管理员不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		if err.Error() == "验证码错误" || err.Error() == "验证码已过期或不存在" {
			utils.Unauthorized(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "密码重置成功", admin)
}

// ResetUserPassword 管理员重置用户密码
// @Summary 管理员重置用户密码
// @Description 管理员重置指定用户的密码
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param admin_id path uint true "管理员ID"
// @Param user_id path uint true "用户ID"
// @Param request body model.AdminResetUserPasswordRequest true "重置用户密码请求参数"
// @Success 200 {object} utils.Response{data=model.UserResponse} "重置成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "权限不足"
// @Failure 404 {object} utils.Response "管理员或用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/{admin_id}/user/{user_id}/reset-password [put]
func (h *AdminHandler) ResetUserPassword(c *gin.Context) {
	// 1. 获取管理员ID
	adminIDStr := c.Param("admin_id")
	if adminIDStr == "" {
		utils.BadRequest(c, "管理员ID不能为空")
		return
	}

	adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "管理员ID格式错误")
		return
	}

	// 2. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 3. 解析请求参数
	var req model.AdminResetUserPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 4. 重置用户密码
	user, err := h.adminService.ResetUserPassword(uint(adminID), uint(userID), &req)
	if err != nil {
		if err.Error() == "管理员不存在" || err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		if err.Error() == "权限不足，只有超级管理员可以重置用户密码" {
			utils.Forbidden(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "用户密码重置成功", user)
}


