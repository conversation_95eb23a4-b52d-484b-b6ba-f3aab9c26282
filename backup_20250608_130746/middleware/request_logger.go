package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// RequestLogger 详细的请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			// 重新设置请求体，以便后续处理器可以读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应写入器来捕获响应
		responseWriter := &responseBodyWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = responseWriter

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录详细日志
		logRequestDetails(c, requestBody, responseWriter.body.Bytes(), duration)
	}
}

// responseBodyWriter 用于捕获响应体的写入器
type responseBodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (r *responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

// logRequestDetails 记录详细的请求信息
func logRequestDetails(c *gin.Context, requestBody []byte, responseBody []byte, duration time.Duration) {
	// 只记录特定路径的详细日志
	path := c.Request.URL.Path
	if !shouldLogDetails(path) {
		return
	}

	// 构建日志信息
	logInfo := map[string]interface{}{
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
		"method":        c.Request.Method,
		"path":          path,
		"status_code":   c.Writer.Status(),
		"duration_ms":   duration.Milliseconds(),
		"client_ip":     c.ClientIP(),
		"user_agent":    c.Request.UserAgent(),
		"content_type":  c.Request.Header.Get("Content-Type"),
		"content_length": c.Request.ContentLength,
	}

	// 添加请求参数
	if len(c.Request.URL.RawQuery) > 0 {
		logInfo["query_params"] = c.Request.URL.RawQuery
	}

	// 添加请求体（如果是JSON）
	if len(requestBody) > 0 && isJSONContent(c.Request.Header.Get("Content-Type")) {
		var requestJSON interface{}
		if err := json.Unmarshal(requestBody, &requestJSON); err == nil {
			// 脱敏处理
			sanitizedRequest := sanitizeRequestData(requestJSON)
			logInfo["request_body"] = sanitizedRequest
		} else {
			logInfo["request_body"] = string(requestBody)
		}
	}

	// 添加响应体（如果是JSON且不太大）
	if len(responseBody) > 0 && len(responseBody) < 1024 && isJSONContent(c.Writer.Header().Get("Content-Type")) {
		var responseJSON interface{}
		if err := json.Unmarshal(responseBody, &responseJSON); err == nil {
			logInfo["response_body"] = responseJSON
		}
	}

	// 添加错误信息（如果有）
	if len(c.Errors) > 0 {
		logInfo["errors"] = c.Errors.String()
	}

	// 格式化输出日志
	logJSON, _ := json.MarshalIndent(logInfo, "", "  ")
	
	// 根据状态码使用不同的日志级别
	statusCode := c.Writer.Status()
	if statusCode >= 400 {
		fmt.Printf("\n🔴 [ERROR REQUEST] %s\n%s\n", path, string(logJSON))
	} else if statusCode >= 300 {
		fmt.Printf("\n🟡 [REDIRECT REQUEST] %s\n%s\n", path, string(logJSON))
	} else {
		fmt.Printf("\n🟢 [SUCCESS REQUEST] %s\n%s\n", path, string(logJSON))
	}
}

// shouldLogDetails 判断是否需要记录详细日志
func shouldLogDetails(path string) bool {
	// 需要详细日志的路径
	detailPaths := []string{
		"/api/v1/user/register",
		"/api/v1/user/login",
		"/api/v1/user/send-code",
		"/api/v1/user/forgot-password",
		"/api/v1/user/reset-password",
		"/api/v1/admin/login",
		"/api/v1/admin/forgot-password",
		"/api/v1/admin/reset-password",
	}

	for _, detailPath := range detailPaths {
		if strings.HasPrefix(path, detailPath) {
			return true
		}
	}

	return false
}

// isJSONContent 判断是否是JSON内容
func isJSONContent(contentType string) bool {
	return strings.Contains(strings.ToLower(contentType), "application/json")
}

// sanitizeRequestData 脱敏处理请求数据
func sanitizeRequestData(data interface{}) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			lowerKey := strings.ToLower(key)
			if lowerKey == "password" || lowerKey == "old_password" || lowerKey == "new_password" {
				result[key] = "***"
			} else if lowerKey == "phone" {
				if phoneStr, ok := value.(string); ok && len(phoneStr) >= 7 {
					result[key] = phoneStr[:3] + "****" + phoneStr[len(phoneStr)-4:]
				} else {
					result[key] = value
				}
			} else {
				result[key] = sanitizeRequestData(value)
			}
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, item := range v {
			result[i] = sanitizeRequestData(item)
		}
		return result
	default:
		return v
	}
}

// SimpleRequestLogger 简单的请求日志中间件（用于非详细路径）
func SimpleRequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)

		// 只记录基本信息
		path := c.Request.URL.Path
		if !shouldLogDetails(path) && !strings.HasPrefix(path, "/health") {
			fmt.Printf("[%s] %s %s %d %v %s\n",
				time.Now().Format("2006/01/02 - 15:04:05"),
				c.Request.Method,
				path,
				c.Writer.Status(),
				duration,
				c.ClientIP(),
			)
		}
	}
}
