package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthResult 认证结果结构体
type AuthResult struct {
	User        *model.User
	Application *model.Application
	AuthMethod  string
	HasBalance  bool
	ServicePrice float64
}

// UnifiedAuth 统一认证中间件 - 一次性完成所有认证和验证
func UnifiedAuth() gin.HandlerFunc {
	// 预创建Repository实例，避免重复创建
	db := database.GetDB()
	appRepo := repository.NewApplicationRepository(db)
	userRepo := repository.NewUserRepository(db)
	priceRepo := repository.NewPriceConfigRepository(db)

	return func(c *gin.Context) {
		// 跳过不需要认证的接口
		if shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 1. 提取认证信息
		authInfo, err := extractAuthInfo(c)
		if err != nil {
			utils.BadRequest(c, "认证信息解析失败: "+err.Error())
			c.Abort()
			return
		}

		if authInfo.AppKey == "" || authInfo.SecretKey == "" {
			utils.Unauthorized(c, "缺少API密钥，请在请求头或请求体中提供app_key和secret_key")
			c.Abort()
			return
		}

		// 2. 验证应用和用户（一次性查询）
		app, user, err := validateAppAndUser(appRepo, userRepo, authInfo.AppKey, authInfo.SecretKey)
		if err != nil {
			utils.Unauthorized(c, err.Error())
			c.Abort()
			return
		}

		// 3. 检查余额和获取价格（如果需要扣费）
		var hasBalance bool
		var servicePrice float64
		if needsBalanceCheck(c.Request.URL.Path) {
			hasBalance, servicePrice, err = checkBalanceAndPrice(priceRepo, user, app.Type)
			if err != nil {
				if err.Error() == "余额不足" {
					utils.PaymentRequired(c, err.Error())
				} else {
					utils.ServerError(c, "余额检查失败: "+err.Error())
				}
				c.Abort()
				return
			}
		} else {
			hasBalance = true // 不需要扣费的接口默认通过
		}

		// 4. 验证签名（如果提供了签名信息）
		if authInfo.Signature != "" {
			if !validateSignature(c, app.SecretKey, authInfo) {
				utils.Unauthorized(c, "签名验证失败")
				c.Abort()
				return
			}
		}

		// 5. 构建认证结果并存储到上下文
		authResult := &AuthResult{
			User:         user,
			Application:  app,
			AuthMethod:   authInfo.Method,
			HasBalance:   hasBalance,
			ServicePrice: servicePrice,
		}

		// 存储认证结果到上下文（统一的数据访问）
		c.Set("auth_result", authResult)
		c.Set("user", user)
		c.Set("app", app)
		c.Set("user_id", user.ID)
		c.Set("app_id", app.ID)
		c.Set("app_type", app.Type)
		c.Set("auth_method", authInfo.Method)
		c.Set("service_price", servicePrice)

		c.Next()
	}
}

// AuthInfo 认证信息结构体
type AuthInfo struct {
	AppKey    string
	SecretKey string
	Method    string
	Timestamp int64
	Signature string
}

// extractAuthInfo 提取认证信息（支持多种认证方式）
func extractAuthInfo(c *gin.Context) (*AuthInfo, error) {
	authInfo := &AuthInfo{}

	// 方式1: 请求头认证（推荐）
	headerAppKey := c.GetHeader("X-App-Key")
	headerSecretKey := c.GetHeader("X-Secret-Key")

	if headerAppKey != "" && headerSecretKey != "" {
		authInfo.AppKey = headerAppKey
		authInfo.SecretKey = headerSecretKey
		authInfo.Method = "header"
		return authInfo, nil
	}

	// 方式2: 请求体认证（备选）
	bodyAppKey, bodySecretKey, err := extractAuthFromBody(c)
	if err != nil {
		return nil, err
	}

	if bodyAppKey != "" && bodySecretKey != "" {
		authInfo.AppKey = bodyAppKey
		authInfo.SecretKey = bodySecretKey
		authInfo.Method = "body"
	}

	// 方式3: 签名认证（高安全性）
	if signature := c.GetHeader("X-Signature"); signature != "" {
		authInfo.Signature = signature
		if timestamp := c.GetHeader("X-Timestamp"); timestamp != "" {
			// 解析时间戳
			// 这里简化处理，实际应该解析时间戳
		}
		authInfo.Method = "signature"
	}

	return authInfo, nil
}

// validateAppAndUser 验证应用和用户（一次性查询，避免重复）
func validateAppAndUser(appRepo *repository.ApplicationRepository, userRepo *repository.UserRepository, appKey, secretKey string) (*model.Application, *model.User, error) {
	// 验证应用密钥
	app, err := appRepo.GetByAppKey(appKey)
	if err != nil {
		return nil, nil, fmt.Errorf("验证API密钥失败")
	}

	if app == nil {
		return nil, nil, fmt.Errorf("无效的AppKey")
	}

	// 验证SecretKey
	if app.SecretKey != secretKey {
		return nil, nil, fmt.Errorf("无效的SecretKey")
	}

	// 检查应用状态
	if app.IsFrozen() {
		return nil, nil, fmt.Errorf("应用已被冻结")
	}

	// 查询用户信息
	user, err := userRepo.GetByID(app.UserID)
	if err != nil {
		return nil, nil, fmt.Errorf("查询用户信息失败")
	}

	if user == nil {
		return nil, nil, fmt.Errorf("用户不存在")
	}

	if user.IsFrozen() {
		return nil, nil, fmt.Errorf("用户账户已被冻结")
	}

	return app, user, nil
}

// checkBalanceAndPrice 检查余额和获取价格（一次性完成）
func checkBalanceAndPrice(priceRepo *repository.PriceConfigRepository, user *model.User, appType int) (bool, float64, error) {
	// 获取服务价格
	price, err := priceRepo.GetPrice(appType, user.ID)
	if err != nil {
		return false, 0, fmt.Errorf("获取服务价格失败: %w", err)
	}

	// 检查余额
	if user.Balance < price {
		return false, price, fmt.Errorf("余额不足")
	}

	return true, price, nil
}

// shouldSkipAuth 判断是否跳过认证
func shouldSkipAuth(path string) bool {
	skipPaths := []string{
		"/health",
		"/api/v1/admin",
		"/api/v1/user",
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// needsBalanceCheck 判断是否需要余额检查
func needsBalanceCheck(path string) bool {
	balanceCheckPaths := []string{
		"/api/v1/api/search",
		// 可以添加其他需要扣费的接口
	}

	for _, checkPath := range balanceCheckPaths {
		if strings.HasPrefix(path, checkPath) {
			return true
		}
	}
	return false
}

// validateSignature 验证签名
func validateSignature(c *gin.Context, secretKey string, authInfo *AuthInfo) bool {
	if authInfo.Signature == "" {
		return true // 没有签名信息，跳过验证
	}

	// 读取请求体用于签名验证
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return false
	}

	// 重新设置请求体
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	// 构建签名字符串：method + url + timestamp + body
	signString := fmt.Sprintf("%s\n%s\n%d\n%s",
		c.Request.Method,
		c.Request.URL.Path,
		authInfo.Timestamp,
		string(body))

	// 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(signString))
	expectedSignature := hex.EncodeToString(h.Sum(nil))

	return hmac.Equal([]byte(authInfo.Signature), []byte(expectedSignature))
}

// extractAuthFromBody 从请求体提取认证信息（复用原有逻辑）
func extractAuthFromBody(c *gin.Context) (string, string, error) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return "", "", err
	}

	// 重新设置请求体，以便后续处理器可以读取
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	// 解析JSON
	var requestData map[string]interface{}
	if err := json.Unmarshal(body, &requestData); err != nil {
		return "", "", err
	}

	appKey, _ := requestData["app_key"].(string)
	secretKey, _ := requestData["secret_key"].(string)

	return appKey, secretKey, nil
}

// GetAuthResult 从上下文获取认证结果
func GetAuthResult(c *gin.Context) (*AuthResult, bool) {
	if result, exists := c.Get("auth_result"); exists {
		if authResult, ok := result.(*AuthResult); ok {
			return authResult, true
		}
	}
	return nil, false
}

// GetUserFromAuthResult 从认证结果获取用户信息
func GetUserFromAuthResult(c *gin.Context) (*model.User, bool) {
	if authResult, exists := GetAuthResult(c); exists {
		return authResult.User, true
	}
	return nil, false
}

// GetAppFromAuthResult 从认证结果获取应用信息
func GetAppFromAuthResult(c *gin.Context) (*model.Application, bool) {
	if authResult, exists := GetAuthResult(c); exists {
		return authResult.Application, true
	}
	return nil, false
}
