package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

type BalanceService struct {
	userRepo       *repository.UserRepository
	balanceLogRepo *repository.BalanceLogRepository
	priceConfigRepo *repository.PriceConfigRepository
}

// NewBalanceService 创建余额服务实例
func NewBalanceService(
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
	priceConfigRepo *repository.PriceConfigRepository,
) *BalanceService {
	return &BalanceService{
		userRepo:       userRepo,
		balanceLogRepo: balanceLogRepo,
		priceConfigRepo: priceConfigRepo,
	}
}

// Recharge 用户充值
func (s *BalanceService) Recharge(userID uint, req *model.UserRechargeRequest) (*model.BalanceLogResponse, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 更新用户余额
	oldBalance := user.Balance
	user.Balance += req.Amount

	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("更新用户余额失败: %w", err)
	}

	// 3. 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      req.Amount,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeRecharge,
		Description: req.Description,
		RelatedID:   0,
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 回滚余额
		user.Balance = oldBalance
		s.userRepo.Update(user)
		return nil, fmt.Errorf("记录充值日志失败: %w", err)
	}

	return balanceLog.ToResponse(), nil
}

// Consume 消费扣费
func (s *BalanceService) Consume(userID uint, serviceType int, amount float64, description string, relatedID uint) (*model.BalanceLogResponse, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查余额是否足够
	if user.Balance < amount {
		return nil, fmt.Errorf("余额不足")
	}

	// 3. 扣费
	oldBalance := user.Balance
	user.Balance -= amount

	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("扣费失败: %w", err)
	}

	// 4. 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -amount, // 消费为负数
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: description,
		RelatedID:   relatedID,
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 回滚余额
		user.Balance = oldBalance
		s.userRepo.Update(user)
		return nil, fmt.Errorf("记录消费日志失败: %w", err)
	}

	return balanceLog.ToResponse(), nil
}

// Refund 退款
func (s *BalanceService) Refund(userID uint, amount float64, description string, relatedID uint) (*model.BalanceLogResponse, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 退款
	oldBalance := user.Balance
	user.Balance += amount

	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("退款失败: %w", err)
	}

	// 3. 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      amount,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeRefund,
		Description: description,
		RelatedID:   relatedID,
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 回滚余额
		user.Balance = oldBalance
		s.userRepo.Update(user)
		return nil, fmt.Errorf("记录退款日志失败: %w", err)
	}

	return balanceLog.ToResponse(), nil
}

// GetBalance 获取用户余额
func (s *BalanceService) GetBalance(userID uint) (float64, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return 0, fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return 0, fmt.Errorf("用户不存在")
	}

	return user.Balance, nil
}

// GetBalanceLogs 获取用户余额变动日志
func (s *BalanceService) GetBalanceLogs(userID uint, page, pageSize int) ([]*model.BalanceLogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.balanceLogRepo.GetByUserID(userID, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询余额日志失败: %w", err)
	}

	var result []*model.BalanceLogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetBalanceLogsByType 根据类型获取余额变动日志
func (s *BalanceService) GetBalanceLogsByType(userID uint, logType int, page, pageSize int) ([]*model.BalanceLogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.balanceLogRepo.GetByType(userID, logType, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询余额日志失败: %w", err)
	}

	var result []*model.BalanceLogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetBalanceStats 获取用户余额统计
func (s *BalanceService) GetBalanceStats(userID uint) (map[string]interface{}, error) {
	// 获取当前余额
	balance, err := s.GetBalance(userID)
	if err != nil {
		return nil, err
	}

	// 获取总充值金额
	totalRecharge, err := s.balanceLogRepo.GetUserTotalRecharge(userID)
	if err != nil {
		return nil, fmt.Errorf("获取总充值金额失败: %w", err)
	}

	// 获取总消费金额
	totalConsume, err := s.balanceLogRepo.GetUserTotalConsume(userID)
	if err != nil {
		return nil, fmt.Errorf("获取总消费金额失败: %w", err)
	}

	// 获取总退款金额
	totalRefund, err := s.balanceLogRepo.GetUserTotalRefund(userID)
	if err != nil {
		return nil, fmt.Errorf("获取总退款金额失败: %w", err)
	}

	// 获取最近的余额日志
	recentLogs, err := s.balanceLogRepo.GetRecentLogs(userID, 5)
	if err != nil {
		return nil, fmt.Errorf("获取最近日志失败: %w", err)
	}

	var recentLogResponses []*model.BalanceLogResponse
	for _, log := range recentLogs {
		recentLogResponses = append(recentLogResponses, log.ToResponse())
	}

	return map[string]interface{}{
		"current_balance": balance,
		"total_recharge":  totalRecharge,
		"total_consume":   totalConsume,
		"total_refund":    totalRefund,
		"recent_logs":     recentLogResponses,
	}, nil
}

// GetServicePrice 获取服务价格
func (s *BalanceService) GetServicePrice(serviceType int, userID uint) (float64, error) {
	return s.priceConfigRepo.GetPrice(serviceType, userID)
}

// CheckBalance 检查余额是否足够
func (s *BalanceService) CheckBalance(userID uint, serviceType int) (bool, float64, error) {
	// 获取用户余额
	balance, err := s.GetBalance(userID)
	if err != nil {
		return false, 0, err
	}

	// 获取服务价格
	price, err := s.GetServicePrice(serviceType, userID)
	if err != nil {
		return false, 0, err
	}

	return balance >= price, price, nil
}
