package service

import (
	"context"
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"

	"github.com/redis/go-redis/v9"
)

// SyncService 数据同步服务
type SyncService struct {
	questionRepo      *repository.QuestionRepository
	questionCacheRepo *repository.QuestionCacheRepository
	rdb               *redis.Client
}

// NewSyncService 创建数据同步服务实例
func NewSyncService(
	questionRepo *repository.QuestionRepository,
	questionCacheRepo *repository.QuestionCacheRepository,
	rdb *redis.Client,
) *SyncService {
	return &SyncService{
		questionRepo:      questionRepo,
		questionCacheRepo: questionCacheRepo,
		rdb:               rdb,
	}
}

// SyncFromMySQLToRedis 从MySQL同步数据到Redis
func (s *SyncService) SyncFromMySQLToRedis() error {
	if s.rdb == nil {
		return fmt.Errorf("Redis不可用，无法同步")
	}

	fmt.Println("🔄 开始从MySQL同步数据到Redis...")
	startTime := time.Now()

	// 分批获取MySQL中的所有题目
	pageSize := 100
	page := 1
	totalSynced := 0

	for {
		offset := (page - 1) * pageSize
		questions, total, err := s.questionRepo.List(offset, pageSize)
		if err != nil {
			return fmt.Errorf("获取MySQL数据失败: %w", err)
		}

		if len(questions) == 0 {
			break
		}

		// 批量同步到Redis
		questionMap := make(map[string]*model.Question)
		for _, question := range questions {
			questionMap[question.CacheKey] = question
		}

		if err := s.questionCacheRepo.BatchSet(questionMap); err != nil {
			fmt.Printf("⚠️ 批量同步第%d页失败: %v\n", page, err)
		} else {
			totalSynced += len(questions)
			fmt.Printf("✅ 已同步第%d页，共%d条记录\n", page, len(questions))
		}

		// 如果已经是最后一页，退出
		if int64(page*pageSize) >= total {
			break
		}

		page++
		
		// 避免过于频繁的操作，稍作延迟
		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(startTime)
	fmt.Printf("🎉 同步完成！共同步%d条记录，耗时%v\n", totalSynced, duration)
	
	return nil
}

// CheckRedisHealth 检查Redis健康状态
func (s *SyncService) CheckRedisHealth() bool {
	if s.rdb == nil {
		return false
	}

	ctx := context.Background()
	_, err := s.rdb.Ping(ctx).Result()
	return err == nil
}

// GetSyncStats 获取同步统计信息
func (s *SyncService) GetSyncStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// MySQL统计
	mysqlTotal, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取MySQL统计失败: %w", err)
	}
	stats["mysql_total"] = mysqlTotal

	// Redis统计
	redisStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		stats["redis_available"] = false
		stats["redis_total"] = 0
	} else {
		stats["redis_available"] = redisStats["redis_available"]
		stats["redis_total"] = redisStats["total_keys"]
	}

	// 健康状态
	stats["redis_healthy"] = s.CheckRedisHealth()
	
	return stats, nil
}

// AutoSync 自动同步服务（定期检查并同步）
func (s *SyncService) AutoSync(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	fmt.Printf("🚀 启动自动同步服务，检查间隔: %v\n", interval)

	for {
		select {
		case <-ticker.C:
			if s.CheckRedisHealth() {
				// Redis健康，检查是否需要同步
				stats, err := s.GetSyncStats()
				if err != nil {
					fmt.Printf("⚠️ 获取同步统计失败: %v\n", err)
					continue
				}

				mysqlTotal := stats["mysql_total"].(int64)
				redisTotal := stats["redis_total"].(int)

				// 如果Redis中的数据明显少于MySQL，进行同步
				if int64(redisTotal) < mysqlTotal*8/10 { // 少于80%时同步
					fmt.Printf("📊 检测到数据不一致: MySQL=%d, Redis=%d，开始同步...\n", mysqlTotal, redisTotal)
					if err := s.SyncFromMySQLToRedis(); err != nil {
						fmt.Printf("⚠️ 自动同步失败: %v\n", err)
					}
				}
			} else {
				fmt.Printf("⚠️ Redis不健康，跳过同步检查\n")
			}
		}
	}
}

// ClearRedisCache 清空Redis缓存
func (s *SyncService) ClearRedisCache() error {
	return s.questionCacheRepo.Clear()
}

// ForceSync 强制同步（清空Redis后重新同步）
func (s *SyncService) ForceSync() error {
	fmt.Println("🔄 开始强制同步...")
	
	// 1. 清空Redis缓存
	if err := s.ClearRedisCache(); err != nil {
		return fmt.Errorf("清空Redis缓存失败: %w", err)
	}
	
	// 2. 从MySQL重新同步
	return s.SyncFromMySQLToRedis()
}
