package utils

import (
	"fmt"
	"solve_api/internal/config"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// SMSService 短信服务接口
type SMSService interface {
	SendCode(phone, code string) error
}

// AliyunSMSService 阿里云短信服务
type AliyunSMSService struct {
	client *dysmsapi20170525.Client
	config *config.SMSConfig
}

// MockSMSService 模拟短信服务
type MockSMSService struct{}

// NewSMSService 创建短信服务实例
func NewSMSService(cfg *config.SMSConfig) (SMSService, error) {
	switch cfg.Provider {
	case "aliyun":
		return NewAliyunSMSService(cfg)
	case "mock":
		return &MockSMSService{}, nil
	default:
		return &MockSMSService{}, nil
	}
}

// NewAliyunSMSService 创建阿里云短信服务
func NewAliyunSMSService(cfg *config.SMSConfig) (*AliyunSMSService, error) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(cfg.AccessKeyID),
		AccessKeySecret: tea.String(cfg.AccessKeySecret),
	}
	
	// 设置访问的域名
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	
	client, err := dysmsapi20170525.NewClient(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create aliyun sms client: %w", err)
	}

	return &AliyunSMSService{
		client: client,
		config: cfg,
	}, nil
}

// SendCode 发送验证码
func (s *AliyunSMSService) SendCode(phone, code string) error {
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(s.config.SignName),
		TemplateCode:  tea.String(s.config.TemplateCode),
		TemplateParam: tea.String(fmt.Sprintf(`{"code":"%s"}`, code)),
	}

	runtime := &util.RuntimeOptions{}
	
	resp, err := s.client.SendSmsWithOptions(sendSmsRequest, runtime)
	if err != nil {
		return fmt.Errorf("failed to send sms: %w", err)
	}

	// 检查响应状态
	if resp.Body.Code == nil || *resp.Body.Code != "OK" {
		message := "unknown error"
		if resp.Body.Message != nil {
			message = *resp.Body.Message
		}
		return fmt.Errorf("sms send failed: %s", message)
	}

	return nil
}

// SendCode 模拟发送验证码
func (s *MockSMSService) SendCode(phone, code string) error {
	// 模拟发送，在控制台输出
	fmt.Printf("【模拟短信】发送验证码到 %s: %s\n", MaskPhone(phone), code)
	return nil
}

// ValidateSMSConfig 验证短信配置
func ValidateSMSConfig(cfg *config.SMSConfig) error {
	if cfg.Provider == "aliyun" {
		if cfg.AccessKeyID == "" {
			return fmt.Errorf("aliyun sms access_key_id is required")
		}
		if cfg.AccessKeySecret == "" {
			return fmt.Errorf("aliyun sms access_key_secret is required")
		}
		if cfg.SignName == "" {
			return fmt.Errorf("aliyun sms sign_name is required")
		}
		if cfg.TemplateCode == "" {
			return fmt.Errorf("aliyun sms template_code is required")
		}
	}
	return nil
}
