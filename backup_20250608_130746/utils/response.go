package utils

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 响应状态码常量
const (
	CodeSuccess      = 200  // 成功
	CodeBadRequest   = 400  // 请求参数错误
	CodeUnauthorized = 401  // 未授权
	CodePaymentRequired = 402  // 需要付费
	CodeForbidden    = 403  // 禁止访问
	CodeNotFound     = 404  // 资源不存在
	CodeConflict     = 409  // 资源冲突
	CodeTooManyReq   = 429  // 请求过于频繁
	CodeServerError  = 500  // 服务器内部错误
	CodeServiceUnavailable = 503 // 服务不可用
)

// 错误消息常量
const (
	MsgSuccess           = "success"
	MsgBadRequest        = "请求参数错误"
	MsgUnauthorized      = "未授权访问"
	MsgPaymentRequired   = "需要付费"
	MsgForbidden         = "禁止访问"
	MsgNotFound          = "资源不存在"
	MsgConflict          = "资源冲突"
	MsgTooManyRequests   = "请求过于频繁"
	MsgServerError       = "服务器内部错误"
	MsgServiceUnavailable = "服务不可用"
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
	})
}

// SuccessWithMessage 成功响应（自定义消息）
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	var httpStatus int
	switch code {
	case CodeBadRequest:
		httpStatus = http.StatusBadRequest
	case CodeUnauthorized:
		httpStatus = http.StatusUnauthorized
	case CodePaymentRequired:
		httpStatus = http.StatusPaymentRequired
	case CodeForbidden:
		httpStatus = http.StatusForbidden
	case CodeNotFound:
		httpStatus = http.StatusNotFound
	case CodeConflict:
		httpStatus = http.StatusConflict
	case CodeTooManyReq:
		httpStatus = http.StatusTooManyRequests
	case CodeServerError:
		httpStatus = http.StatusInternalServerError
	case CodeServiceUnavailable:
		httpStatus = http.StatusServiceUnavailable
	default:
		httpStatus = http.StatusInternalServerError
	}

	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
	})
}

// BadRequest 400错误
func BadRequest(c *gin.Context, message string) {
	Error(c, CodeBadRequest, message)
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message string) {
	Error(c, CodeUnauthorized, message)
}

// PaymentRequired 402错误
func PaymentRequired(c *gin.Context, message string) {
	Error(c, CodePaymentRequired, message)
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message string) {
	Error(c, CodeForbidden, message)
}

// NotFound 404错误
func NotFound(c *gin.Context, message string) {
	Error(c, CodeNotFound, message)
}

// Conflict 409错误
func Conflict(c *gin.Context, message string) {
	Error(c, CodeConflict, message)
}

// TooManyRequests 429错误
func TooManyRequests(c *gin.Context, message string) {
	Error(c, CodeTooManyReq, message)
}

// ServerError 500错误
func ServerError(c *gin.Context, message string) {
	Error(c, CodeServerError, message)
}

// ServiceUnavailable 503错误
func ServiceUnavailable(c *gin.Context, message string) {
	Error(c, CodeServiceUnavailable, message)
}

// GetCurrentTimestamp 获取当前时间戳字符串
func GetCurrentTimestamp() string {
	return time.Now().Format("2006-01-02 15:04:05")
}
