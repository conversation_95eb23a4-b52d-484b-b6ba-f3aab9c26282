package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"solve_api/internal/api"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/middleware"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 2. 初始化日志
	logger := initLogger(cfg)
	defer logger.Sync()

	// 3. 初始化数据库管理器
	dbManager := database.GetDatabaseManager()
	if err := dbManager.Initialize(cfg); err != nil {
		logger.Fatal("Failed to initialize database manager", zap.Error(err))
	}
	defer dbManager.Close()

	// 4. 自动迁移数据库表
	if err := autoMigrate(dbManager); err != nil {
		logger.Fatal("Failed to migrate database", zap.Error(err))
	}

	// 5. 初始化系统配置
	if err := initSystemConfig(dbManager); err != nil {
		logger.Fatal("Failed to initialize system config", zap.Error(err))
	}

	// 6. 初始化短信服务
	smsService, err := utils.NewSMSService(&cfg.SMS)
	if err != nil {
		logger.Fatal("Failed to initialize SMS service", zap.Error(err))
	}

	// 7. 获取统一的数据库和缓存组件
	db := dbManager.GetMySQL()
	redis := dbManager.GetRedis()
	configCache := dbManager.GetConfigCache()

	// 8. 初始化Repository（使用统一的数据库连接）
	userRepo := repository.NewUserRepository(db)
	configRepo := repository.NewConfigRepository(db)
	appRepo := repository.NewApplicationRepository(db)
	adminRepo := repository.NewAdminRepository(db)
	modelConfigRepo := repository.NewModelConfigRepository(db)
	priceConfigRepo := repository.NewPriceConfigRepository(db)
	questionRepo := repository.NewQuestionRepository(db)
	questionCacheRepo := repository.NewQuestionCacheRepository(redis, questionRepo)
	balanceLogRepo := repository.NewBalanceLogRepository(db)
	apiLogRepo := repository.NewAPILogRepository(db)
	statsRepo := repository.NewStatsRepository(db)

	// 9. 初始化Service（使用配置缓存优化）
	userService := service.NewUserService(userRepo, configRepo, smsService)
	appService := service.NewApplicationService(appRepo, userRepo)
	adminService := service.NewAdminService(adminRepo, userRepo, redis, smsService)
	modelConfigService := service.NewModelConfigService(modelConfigRepo)
	priceService := service.NewPriceService(priceConfigRepo)
	balanceService := service.NewBalanceService(userRepo, balanceLogRepo, priceConfigRepo)
	aiLogService := service.NewAILogService()
	aiService := service.NewAIService(modelConfigRepo, aiLogService)
	questionService := service.NewQuestionService(questionRepo, questionCacheRepo, aiService, userRepo, balanceLogRepo)
	apiLogService := service.NewAPILogService(apiLogRepo)
	statsService := service.NewStatsService(statsRepo, apiLogRepo, userRepo, appRepo, questionRepo)
	configService := service.NewConfigService(configRepo)
	syncService := service.NewSyncService(questionRepo, questionCacheRepo, redis)

	// 启动自动同步服务（每30分钟检查一次）
	go syncService.AutoSync(30 * time.Minute)

	// 10. 初始化Handler
	userHandler := api.NewUserHandler(userService)
	appHandler := api.NewApplicationHandler(appService)
	adminHandler := api.NewAdminHandler(adminService)
	modelConfigHandler := api.NewModelConfigHandler(modelConfigService)
	priceHandler := api.NewPriceHandler(priceService)
	balanceHandler := api.NewBalanceHandler(balanceService)
	questionHandler := api.NewQuestionHandler(questionService)
	questionMgmtHandler := api.NewQuestionManagementHandler(questionService)
	statsHandler := api.NewStatsHandler(statsService, apiLogService)
	systemHandler := api.NewSystemHandler(configService, statsService, apiLogService)
	logHandler := api.NewLogHandler(apiLogService)
	aiLogHandler := api.NewAILogHandler(aiLogService)

	// 11. 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 12. 创建路由（使用统一中间件）
	router := setupOptimizedRouter(logger, dbManager, configCache, userHandler, appHandler, adminHandler, modelConfigHandler, priceHandler, balanceHandler, questionHandler, questionMgmtHandler, statsHandler, systemHandler, logHandler, aiLogHandler)

	// 13. 启动服务器
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	logger.Info("Starting optimized server",
		zap.String("addr", addr),
		zap.Bool("mysql_connected", dbManager.IsHealthy()["mysql"]),
		zap.Bool("redis_connected", dbManager.IsHealthy()["redis"]))

	// 优雅关闭
	go func() {
		if err := router.Run(addr); err != nil {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down gracefully...")
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) *zap.Logger {
	// 配置日志轮转
	writer := &lumberjack.Logger{
		Filename:   cfg.Log.Filename,
		MaxSize:    cfg.Log.MaxSize,
		MaxAge:     cfg.Log.MaxAge,
		MaxBackups: cfg.Log.MaxBackups,
		Compress:   cfg.Log.Compress,
	}

	// 配置日志级别
	var level zapcore.Level
	switch cfg.Log.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 创建核心
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(writer),
		level,
	)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	return logger
}

// setupOptimizedRouter 设置优化后的路由（使用统一中间件）
func setupOptimizedRouter(logger *zap.Logger, dbManager *database.DatabaseManager, configCache *cache.ConfigCache, userHandler *api.UserHandler, appHandler *api.ApplicationHandler, adminHandler *api.AdminHandler, modelConfigHandler *api.ModelConfigHandler, priceHandler *api.PriceHandler, balanceHandler *api.BalanceHandler, questionHandler *api.QuestionHandler, questionMgmtHandler *api.QuestionManagementHandler, statsHandler *api.StatsHandler, systemHandler *api.SystemHandler, logHandler *api.LogHandler, aiLogHandler *api.AILogHandler) *gin.Engine {
	router := gin.New()

	// 添加统一的优化中间件
	router.Use(middleware.CORS())
	router.Use(middleware.UnifiedLogger(logger))    // 统一日志中间件
	router.Use(middleware.UnifiedRecovery(logger))  // 统一恢复中间件

	// 添加限流中间件（针对用户接口）
	router.Use(middleware.RateLimitByIP(100, time.Minute)) // 每分钟100次请求

	// 静态文件服务
	router.Static("/web", "./web")
	router.StaticFile("/logs", "./web/logs.html")

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 用户相关路由
		userGroup := v1.Group("/user")
		{
			userGroup.POST("/register", userHandler.Register)
			userGroup.POST("/login", userHandler.Login)
			userGroup.GET("/profile/:user_id", userHandler.GetProfile)
			userGroup.PUT("/profile/:user_id", userHandler.UpdateProfile)
			userGroup.POST("/send-code", userHandler.SendCode)

			// 用户密码管理
			userGroup.PUT("/:user_id/change-password", userHandler.ChangePassword)
			userGroup.POST("/forgot-password", userHandler.SendForgotPasswordCode)
			userGroup.POST("/reset-password", userHandler.ResetPassword)

			// 用户余额管理
			userGroup.POST("/:user_id/recharge", balanceHandler.Recharge)
			userGroup.GET("/:user_id/balance", balanceHandler.GetBalance)
			userGroup.GET("/:user_id/balance/logs", balanceHandler.GetBalanceLogs)
			userGroup.GET("/:user_id/balance/stats", balanceHandler.GetBalanceStats)
			userGroup.GET("/:user_id/balance/check", balanceHandler.CheckBalance)
		}

		// 应用管理路由
		appGroup := v1.Group("/user/:user_id/app")
		{
			appGroup.POST("", appHandler.Create)
			appGroup.GET("", appHandler.GetList)
			appGroup.GET("/:app_id", appHandler.GetByID)
			appGroup.PUT("/:app_id", appHandler.Update)
			appGroup.PUT("/:app_id/reset-secret", appHandler.ResetSecretKey)
			appGroup.PUT("/:app_id/status", appHandler.UpdateStatus)
		}

		// 管理员相关路由
		adminGroup := v1.Group("/admin")
		{
			adminGroup.POST("/login", adminHandler.Login)
			adminGroup.POST("/forgot-password", adminHandler.SendForgotPasswordCode)
			adminGroup.POST("/reset-password", adminHandler.ResetPassword)

			// 管理员密码管理
			adminGroup.PUT("/:admin_id/change-password", adminHandler.ChangePassword)

			// 管理员操作用户
			adminGroup.PUT("/:admin_id/user/:user_id/reset-password", adminHandler.ResetUserPassword)

			// 模型配置管理（基于名称主键的简化设计）
			modelGroup := adminGroup.Group("/model")
			{
				// 基础接口（保留兼容性）
				modelGroup.POST("", modelConfigHandler.Create)
				modelGroup.GET("", modelConfigHandler.GetList)
				modelGroup.GET("/enabled", modelConfigHandler.GetEnabled)

				// 基于名称的核心接口
				modelGroup.GET("/fixed", modelConfigHandler.GetFixedModels)
				modelGroup.GET("/name/:name", modelConfigHandler.GetByName)
				modelGroup.PUT("/name/:name/params", modelConfigHandler.UpdateParams)
				modelGroup.PUT("/name/:name/apikey", modelConfigHandler.UpdateApiKey)
				modelGroup.PUT("/name/:name/status", modelConfigHandler.UpdateStatusByName)

				// 注意：基于ID的接口已移除，使用name作为主键
				// 注意：删除接口已移除，模型配置不支持删除操作
			}

			// 题目管理
			questionGroup := adminGroup.Group("/question")
			{
				questionGroup.GET("", questionHandler.GetList)
				questionGroup.GET("/stats", questionHandler.GetStats)
				questionGroup.GET("/search", questionHandler.SearchQuestions)
				questionGroup.GET("/subject/:subject", questionHandler.GetBySubject)
				questionGroup.GET("/:id", questionHandler.GetByID)
				questionGroup.POST("/cache/clear", questionHandler.ClearCache)
			}

			// 题库管理（新增）
			questionMgmtGroup := adminGroup.Group("/question-management")
			{
				questionMgmtGroup.POST("", questionMgmtHandler.CreateQuestion)
				questionMgmtGroup.GET("", questionMgmtHandler.GetQuestionList)
				questionMgmtGroup.GET("/:id", questionMgmtHandler.GetQuestion)
				questionMgmtGroup.PUT("/:id", questionMgmtHandler.UpdateQuestion)
				questionMgmtGroup.DELETE("/:id", questionMgmtHandler.DeleteQuestion)
			}

			// 价格配置管理
			priceGroup := adminGroup.Group("/price")
			{
				priceGroup.POST("", priceHandler.CreatePriceConfig)
				priceGroup.GET("", priceHandler.GetPriceConfigList)
				priceGroup.GET("/:id", priceHandler.GetPriceConfig)
				priceGroup.PUT("/:id", priceHandler.UpdatePriceConfig)
				priceGroup.DELETE("/:id", priceHandler.DeletePriceConfig)
				priceGroup.GET("/service/:service_type", priceHandler.GetPriceByService)
				priceGroup.PUT("/service/:service_type/default", priceHandler.SetDefaultPrice)
				priceGroup.PUT("/service/:service_type/user/:user_id", priceHandler.SetUserPrice)
			}

			// 用户余额管理（管理员）
			adminGroup.POST("/user/:user_id/refund", balanceHandler.Refund)

			// 统计分析
			statsGroup := adminGroup.Group("/stats")
			{
				statsGroup.GET("/system", statsHandler.GetSystemStats)
				statsGroup.GET("/system/range", statsHandler.GetSystemStatsRange)
				statsGroup.GET("/system/total", statsHandler.GetSystemStatsTotal)
				statsGroup.GET("/user/:user_id", statsHandler.GetUserStats)
				statsGroup.GET("/user/:user_id/range", statsHandler.GetUserStatsRange)
				statsGroup.GET("/user/:user_id/total", statsHandler.GetUserStatsTotal)
				statsGroup.GET("/top-users", statsHandler.GetTopUsers)
				statsGroup.GET("/recent", statsHandler.GetRecentStats)
				statsGroup.POST("/generate", statsHandler.GenerateDailyStats)
			}

			// 日志管理
			logGroup := adminGroup.Group("/logs")
			{
				// API日志管理
				logGroup.GET("/api", logHandler.GetAPILogs)
				logGroup.GET("/api/:id", logHandler.GetAPILogDetail)
				logGroup.GET("/api/stats", logHandler.GetAPILogStats)
				logGroup.GET("/api/export", logHandler.ExportAPILogs)
				logGroup.POST("/api/clean", logHandler.CleanAPILogs)

				// AI模型日志管理
				logGroup.GET("/ai", aiLogHandler.GetLogs)
				logGroup.GET("/ai/:id", aiLogHandler.GetLogByID)
				logGroup.GET("/ai/stats", aiLogHandler.GetLogStats)
				logGroup.POST("/ai/clear", aiLogHandler.ClearLogs)
				logGroup.POST("/ai/clean", aiLogHandler.CleanOldLogs)
				logGroup.GET("/ai/export", aiLogHandler.ExportLogs)

				// 系统日志管理
				logGroup.GET("/system", logHandler.GetSystemLogs)
			}

			// 系统管理
			systemGroup := adminGroup.Group("/system")
			{
				systemGroup.GET("/info", systemHandler.GetSystemInfo)
				systemGroup.GET("/health", systemHandler.GetSystemHealth)
				systemGroup.GET("/dashboard", systemHandler.GetDashboard)
				systemGroup.GET("/metrics", systemHandler.GetSystemMetrics)
				systemGroup.GET("/logs", systemHandler.GetSystemLogs)
				systemGroup.POST("/cleanup", systemHandler.CleanupSystem)
				systemGroup.POST("/backup", systemHandler.BackupSystem)
				systemGroup.POST("/restore", systemHandler.RestoreSystem)
				systemGroup.PUT("/config", systemHandler.UpdateSystemConfig)
			}
		}

		// 业务API路由（使用统一认证中间件）
		apiGroup := v1.Group("/api")
		apiGroup.Use(middleware.UnifiedAuth())  // 统一认证中间件（包含认证、余额检查、权限验证）
		apiGroup.Use(middleware.RateLimit())
		apiGroup.Use(middleware.ParamValidator())
		{
			// 拍照搜题接口
			apiGroup.POST("/search", questionHandler.Search)
		}
	}

	// 公开的AI日志查看API（用于调试）
	router.GET("/api/v1/logs/ai", aiLogHandler.GetLogs)
	router.POST("/api/v1/logs/ai/clear", aiLogHandler.ClearLogs)

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"app":    config.GlobalConfig.App.Name,
			"version": config.GlobalConfig.App.Version,
		})
	})

	return router
}

// autoMigrate 自动迁移数据库表
func autoMigrate(dbManager *database.DatabaseManager) error {
	db := dbManager.GetMySQL()
	if db == nil {
		return fmt.Errorf("MySQL连接不可用")
	}

	return db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
		&model.Application{},
		&model.ModelConfig{},
		&model.PriceConfig{},
		&model.BalanceLog{},
		&model.Question{},
		&model.APILog{},
		&model.SystemStats{},
		&model.UserStats{},
	)
}

// initSystemConfig 初始化系统配置
func initSystemConfig(dbManager *database.DatabaseManager) error {
	db := dbManager.GetMySQL()
	if db == nil {
		return fmt.Errorf("MySQL连接不可用")
	}

	configRepo := repository.NewConfigRepository(db)

	// 初始化默认邀请码
	if err := configRepo.SetValue(
		model.ConfigKeyInviteCode,
		config.GlobalConfig.App.InviteCode,
		"系统邀请码",
	); err != nil {
		return fmt.Errorf("failed to set invite code: %w", err)
	}

	// 初始化默认限流配置
	if err := configRepo.SetValue(
		model.ConfigKeyRateLimit,
		fmt.Sprintf("%d", config.GlobalConfig.App.RateLimit),
		"API限流配置（次/秒）",
	); err != nil {
		return fmt.Errorf("failed to set rate limit: %w", err)
	}

	// 初始化默认缓存TTL
	if err := configRepo.SetValue(
		model.ConfigKeyCacheTTL,
		fmt.Sprintf("%d", config.GlobalConfig.App.CacheTTL),
		"缓存TTL配置（秒）",
	); err != nil {
		return fmt.Errorf("failed to set cache TTL: %w", err)
	}

	return nil
}
