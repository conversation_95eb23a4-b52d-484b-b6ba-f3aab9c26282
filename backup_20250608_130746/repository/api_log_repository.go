package repository

import (
	"solve_api/internal/model"
	"time"

	"gorm.io/gorm"
)

type APILogRepository struct {
	db *gorm.DB
}

// NewAPILogRepository 创建API日志仓库实例
func NewAPILogRepository(db *gorm.DB) *APILogRepository {
	return &APILogRepository{db: db}
}

// Create 创建API日志
func (r *APILogRepository) Create(log *model.APILog) error {
	return r.db.Create(log).Error
}

// GetByID 根据ID获取API日志
func (r *APILogRepository) GetByID(id uint) (*model.APILog, error) {
	var log model.APILog
	err := r.db.First(&log, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// List 获取API日志列表
func (r *APILogRepository) List(offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByUserID 根据用户ID获取API日志列表
func (r *APILogRepository) GetByUserID(userID uint, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("user_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByAppID 根据应用ID获取API日志列表
func (r *APILogRepository) GetByAppID(appID uint, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Where("app_id = ?", appID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("app_id = ?", appID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByDateRange 根据日期范围获取API日志
func (r *APILogRepository) GetByDateRange(startDate, endDate time.Time, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetTotalCallsByUser 获取用户总调用次数
func (r *APILogRepository) GetTotalCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// GetTotalCallsByApp 获取应用总调用次数
func (r *APILogRepository) GetTotalCallsByApp(appID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).Where("app_id = ?", appID).Count(&count).Error
	return count, err
}

// GetSuccessCallsByUser 获取用户成功调用次数
func (r *APILogRepository) GetSuccessCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ? AND status_code >= 200 AND status_code < 300", userID).
		Count(&count).Error
	return count, err
}

// GetErrorCallsByUser 获取用户错误调用次数
func (r *APILogRepository) GetErrorCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ? AND (status_code < 200 OR status_code >= 300)", userID).
		Count(&count).Error
	return count, err
}

// GetTotalCostByUser 获取用户总费用
func (r *APILogRepository) GetTotalCostByUser(userID uint) (float64, error) {
	var total float64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&total).Error
	return total, err
}

// GetAvgResponseTimeByUser 获取用户平均响应时间
func (r *APILogRepository) GetAvgResponseTimeByUser(userID uint) (float64, error) {
	var avg float64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ?", userID).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avg).Error
	return avg, err
}

// GetCallsByDate 获取指定日期的调用统计
func (r *APILogRepository) GetCallsByDate(date string) (int64, int64, int64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var total, success, error int64

	// 总调用次数
	r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Count(&total)

	// 成功调用次数
	r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ? AND status_code >= 200 AND status_code < 300", startTime, endTime).
		Count(&success)

	// 错误调用次数
	error = total - success

	return total, success, error, nil
}

// GetRevenueByDate 获取指定日期的收入
func (r *APILogRepository) GetRevenueByDate(date string) (float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var revenue float64
	err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&revenue).Error

	return revenue, err
}

// GetAvgResponseTimeByDate 获取指定日期的平均响应时间
func (r *APILogRepository) GetAvgResponseTimeByDate(date string) (float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var avg float64
	err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avg).Error

	return avg, err
}

// GetUserCallsByDate 获取指定用户和日期的调用统计
func (r *APILogRepository) GetUserCallsByDate(userID uint, date string) (int64, int64, int64, float64, float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var total, success, errorCount int64
	var totalCost, avgResponseTime float64

	// 总调用次数
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Count(&total)

	// 成功调用次数
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ? AND status_code >= 200 AND status_code < 300", userID, startTime, endTime).
		Count(&success)

	// 错误调用次数
	errorCount = total - success

	// 总费用
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&totalCost)

	// 平均响应时间
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avgResponseTime)

	return total, success, errorCount, totalCost, avgResponseTime, nil
}

// DeleteOldLogs 删除旧日志（保留指定天数）
func (r *APILogRepository) DeleteOldLogs(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return r.db.Where("created_at < ?", cutoffTime).Delete(&model.APILog{}).Error
}

// GetWithFilters 根据过滤条件获取API日志
func (r *APILogRepository) GetWithFilters(filters map[string]interface{}, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 构建查询
	query := r.db.Model(&model.APILog{})

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if limit > 0 {
		query = query.Offset(offset).Limit(limit)
	}

	err := query.Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetStatsWithFilters 根据过滤条件获取统计信息
func (r *APILogRepository) GetStatsWithFilters(filters map[string]interface{}) (map[string]interface{}, error) {
	query := r.db.Model(&model.APILog{})

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	var stats struct {
		TotalCalls      int64   `json:"total_calls"`
		SuccessCalls    int64   `json:"success_calls"`
		ErrorCalls      int64   `json:"error_calls"`
		TotalCost       float64 `json:"total_cost"`
		AvgResponseTime float64 `json:"avg_response_time"`
		TotalRequestSize int64  `json:"total_request_size"`
		TotalResponseSize int64 `json:"total_response_size"`
	}

	// 总调用次数
	if err := query.Count(&stats.TotalCalls).Error; err != nil {
		return nil, err
	}

	// 成功调用次数
	successQuery := r.applyFilters(r.db.Model(&model.APILog{}), filters)
	if err := successQuery.Where("status_code >= 200 AND status_code < 300").Count(&stats.SuccessCalls).Error; err != nil {
		return nil, err
	}

	// 错误调用次数
	stats.ErrorCalls = stats.TotalCalls - stats.SuccessCalls

	// 总费用
	costQuery := r.applyFilters(r.db.Model(&model.APILog{}), filters)
	if err := costQuery.Select("COALESCE(SUM(cost), 0)").Scan(&stats.TotalCost).Error; err != nil {
		return nil, err
	}

	// 平均响应时间
	avgQuery := r.applyFilters(r.db.Model(&model.APILog{}), filters)
	if err := avgQuery.Select("COALESCE(AVG(response_time), 0)").Scan(&stats.AvgResponseTime).Error; err != nil {
		return nil, err
	}

	// 总请求大小
	reqSizeQuery := r.applyFilters(r.db.Model(&model.APILog{}), filters)
	if err := reqSizeQuery.Select("COALESCE(SUM(request_size), 0)").Scan(&stats.TotalRequestSize).Error; err != nil {
		return nil, err
	}

	// 总响应大小
	respSizeQuery := r.applyFilters(r.db.Model(&model.APILog{}), filters)
	if err := respSizeQuery.Select("COALESCE(SUM(response_size), 0)").Scan(&stats.TotalResponseSize).Error; err != nil {
		return nil, err
	}

	// 获取状态码分布
	statusDistribution, err := r.getStatusDistribution(filters)
	if err != nil {
		return nil, err
	}

	// 获取每小时调用量
	hourlyStats, err := r.getHourlyStats(filters)
	if err != nil {
		return nil, err
	}

	result := map[string]interface{}{
		"total_calls":         stats.TotalCalls,
		"success_calls":       stats.SuccessCalls,
		"error_calls":         stats.ErrorCalls,
		"success_rate":        float64(stats.SuccessCalls) / float64(stats.TotalCalls) * 100,
		"total_cost":          stats.TotalCost,
		"avg_response_time":   stats.AvgResponseTime,
		"total_request_size":  stats.TotalRequestSize,
		"total_response_size": stats.TotalResponseSize,
		"status_distribution": statusDistribution,
		"hourly_stats":        hourlyStats,
	}

	return result, nil
}

// applyFilters 应用过滤条件
func (r *APILogRepository) applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if userID, ok := filters["user_id"]; ok {
		query = query.Where("user_id = ?", userID)
	}

	if appID, ok := filters["app_id"]; ok {
		query = query.Where("app_id = ?", appID)
	}

	if method, ok := filters["method"]; ok {
		query = query.Where("method = ?", method)
	}

	if path, ok := filters["path"]; ok {
		query = query.Where("path LIKE ?", "%"+path.(string)+"%")
	}

	if statusCode, ok := filters["status_code"]; ok {
		query = query.Where("status_code = ?", statusCode)
	}

	if startDate, ok := filters["start_date"]; ok {
		startTime, err := time.Parse("2006-01-02", startDate.(string))
		if err == nil {
			query = query.Where("created_at >= ?", startTime)
		}
	}

	if endDate, ok := filters["end_date"]; ok {
		endTime, err := time.Parse("2006-01-02", endDate.(string))
		if err == nil {
			// 结束日期包含当天，所以加一天
			endTime = endTime.AddDate(0, 0, 1)
			query = query.Where("created_at < ?", endTime)
		}
	}

	return query
}

// getStatusDistribution 获取状态码分布
func (r *APILogRepository) getStatusDistribution(filters map[string]interface{}) ([]map[string]interface{}, error) {
	query := r.applyFilters(r.db.Model(&model.APILog{}), filters)

	var results []struct {
		StatusCode int   `json:"status_code"`
		Count      int64 `json:"count"`
	}

	err := query.Select("status_code, COUNT(*) as count").
		Group("status_code").
		Order("status_code").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	var distribution []map[string]interface{}
	for _, result := range results {
		distribution = append(distribution, map[string]interface{}{
			"status_code": result.StatusCode,
			"count":       result.Count,
		})
	}

	return distribution, nil
}

// getHourlyStats 获取每小时统计
func (r *APILogRepository) getHourlyStats(filters map[string]interface{}) ([]map[string]interface{}, error) {
	query := r.applyFilters(r.db.Model(&model.APILog{}), filters)

	var results []struct {
		Hour  int   `json:"hour"`
		Count int64 `json:"count"`
	}

	err := query.Select("HOUR(created_at) as hour, COUNT(*) as count").
		Group("HOUR(created_at)").
		Order("hour").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	var hourlyStats []map[string]interface{}
	for _, result := range results {
		hourlyStats = append(hourlyStats, map[string]interface{}{
			"hour":  result.Hour,
			"count": result.Count,
		})
	}

	return hourlyStats, nil
}
