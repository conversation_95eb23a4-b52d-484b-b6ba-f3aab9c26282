package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type ModelConfigRepository struct {
	db *gorm.DB
}

// NewModelConfigRepository 创建模型配置仓库实例
func NewModelConfigRepository(db *gorm.DB) *ModelConfigRepository {
	return &ModelConfigRepository{db: db}
}

// Create 创建模型配置
func (r *ModelConfigRepository) Create(modelConfig *model.ModelConfig) error {
	return r.db.Create(modelConfig).Error
}

// GetByName 根据名称获取模型配置（主键查询）
func (r *ModelConfigRepository) GetByName(name string) (*model.ModelConfig, error) {
	var modelConfig model.ModelConfig
	err := r.db.Where("name = ?", name).First(&modelConfig).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &modelConfig, nil
}

// ExistsByName 检查模型名称是否存在
func (r *ModelConfigRepository) ExistsByName(name string) (bool, error) {
	var count int64
	err := r.db.Model(&model.ModelConfig{}).Where("name = ?", name).Count(&count).Error
	return count > 0, err
}

// Update 更新模型配置（基于主键name）
func (r *ModelConfigRepository) Update(modelConfig *model.ModelConfig) error {
	return r.db.Save(modelConfig).Error
}

// UpdateByName 根据名称更新模型配置
func (r *ModelConfigRepository) UpdateByName(name string, updates map[string]interface{}) error {
	return r.db.Model(&model.ModelConfig{}).Where("name = ?", name).Updates(updates).Error
}

// 注意：删除模型配置功能已移除
// 模型配置不支持删除操作，以保持系统稳定性和兼容性

// List 获取模型配置列表（固定返回两个模型，支持分页）
func (r *ModelConfigRepository) List(offset, limit int) ([]*model.ModelConfig, int64, error) {
	var modelConfigs []*model.ModelConfig
	var total int64

	// 获取总数（最多2个）
	if err := r.db.Model(&model.ModelConfig{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按名称排序
	err := r.db.Offset(offset).Limit(limit).Order("name").Find(&modelConfigs).Error
	if err != nil {
		return nil, 0, err
	}

	return modelConfigs, total, nil
}

// GetAll 获取所有模型配置（固定返回两个模型）
func (r *ModelConfigRepository) GetAll() ([]*model.ModelConfig, error) {
	var modelConfigs []*model.ModelConfig
	err := r.db.Order("name").Find(&modelConfigs).Error
	if err != nil {
		return nil, err
	}
	return modelConfigs, nil
}

// GetEnabled 获取所有启用的模型配置
func (r *ModelConfigRepository) GetEnabled() ([]*model.ModelConfig, error) {
	var modelConfigs []*model.ModelConfig
	err := r.db.Where("status = ?", model.ModelConfigStatusEnabled).
		Order("name").
		Find(&modelConfigs).Error
	if err != nil {
		return nil, err
	}
	return modelConfigs, nil
}

// GetByStatus 根据状态获取模型配置列表
func (r *ModelConfigRepository) GetByStatus(status int) ([]*model.ModelConfig, error) {
	var modelConfigs []*model.ModelConfig
	err := r.db.Where("status = ?", status).
		Order("name").
		Find(&modelConfigs).Error
	if err != nil {
		return nil, err
	}
	return modelConfigs, nil
}



// UpdateStatusByName 根据名称更新模型状态
func (r *ModelConfigRepository) UpdateStatusByName(name string, status int) error {
	return r.db.Model(&model.ModelConfig{}).
		Where("name = ?", name).
		Update("status", status).Error
}

// GetEnabledByName 根据名称获取启用的模型配置
func (r *ModelConfigRepository) GetEnabledByName(name string) (*model.ModelConfig, error) {
	var modelConfig model.ModelConfig
	err := r.db.Where("name = ? AND status = ?", name, model.ModelConfigStatusEnabled).
		First(&modelConfig).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &modelConfig, nil
}

// GetEnabledCount 获取启用的模型数量
func (r *ModelConfigRepository) GetEnabledCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.ModelConfig{}).
		Where("status = ?", model.ModelConfigStatusEnabled).
		Count(&count).Error
	return count, err
}

// GetTotalCount 获取模型总数
func (r *ModelConfigRepository) GetTotalCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.ModelConfig{}).Count(&count).Error
	return count, err
}
